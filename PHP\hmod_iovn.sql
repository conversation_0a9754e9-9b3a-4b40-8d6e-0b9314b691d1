-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- <PERSON><PERSON><PERSON> chủ: localhost
-- Th<PERSON><PERSON> gian đã tạo: Th5 26, 2025 lúc 03:43 PM
-- <PERSON><PERSON><PERSON> bản m<PERSON>hụ<PERSON> vụ: 10.11.11-MariaDB-ubu2204
-- <PERSON><PERSON><PERSON> bản PHP: 8.0.30

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- <PERSON><PERSON> sở dữ liệu: `hmod_iovn`
--

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `admin`
--

CREATE TABLE `admin` (
  `id` int(11) NOT NULL,
  `hovaten` varchar(255) NOT NULL,
  `taikhoan` varchar(255) NOT NULL,
  `matkhau` varchar(255) NOT NULL,
  `code` varchar(255) NOT NULL,
  `hansudung` varchar(255) NOT NULL,
  `sokeygioihan` varchar(255) NOT NULL,
  `sokeydatao` varchar(255) NOT NULL DEFAULT '0',
  `sokeydaban` varchar(255) NOT NULL DEFAULT '0',
  `level` varchar(255) NOT NULL,
  `trangthai` varchar(255) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Đang đổ dữ liệu cho bảng `admin`
--

INSERT INTO `admin` (`id`, `hovaten`, `taikhoan`, `matkhau`, `code`, `hansudung`, `sokeygioihan`, `sokeydatao`, `sokeydaban`, `level`, `trangthai`) VALUES
(26, 'H-MOD', 'langtudapxe', '1505256ba1d3b2cec43fe6ecbddb9693', 'tranhuong', '2030-03-20', '99999', '21992', '19028', 'admin', 'hoatdong'),
(31, 'H-MOD', 'GarenaMod', '014c72771c955d7331ebcb69e17f531c', 'GarenaMod', '2028-01-31', '1480', '1330', '1070', 'resell', 'hoatdong'),
(32, 'BG-Studio', 'BGS', 'b4509e5d542d28671b50c29011f3b0e3', 'BG-Studio', '2028-01-31', '563', '0', '452', 'resell', 'hoatdong'),
(34, 'Vuong Mod', 'Vuongmod', 'cb18e616637c5636e8c20ce68d2a2f72', 'vuongmod', '2025-03-31', '4168', '0', '3516', 'resell', 'hoatdong'),
(39, 'Hack Map Android', 'tanmodgame', '42308d782d2c697d3fc84052dd804d5a', 'tanmodgame', '2028-02-03', '15200', '13800', '10952', 'resell', 'hoatdong'),
(42, 'P-MOD VN', 'Nguyenphuongkb', '68d8529bdfbf666e1dbdbc720d0b3ec5', 'Nguyenphuongkb', '2025-05-05', '300', '10', '151', 'resell', 'hoatdong'),
(46, 'ModVN', 'ModVN', 'c28f3683edd701849edb8d575170802e', 'ModVN', '2026-05-05', '680', '680', '609', 'resell', 'hoatdong');

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `keytab`
--

CREATE TABLE `keytab` (
  `id` int(11) NOT NULL,
  `tenkey` varchar(255) NOT NULL,
  `ngaykichhoat` varchar(255) NOT NULL DEFAULT '',
  `hansudung` varchar(255) NOT NULL,
  `may` varchar(255) NOT NULL,
  `code` varchar(255) NOT NULL,
  `trangthai` varchar(255) NOT NULL,
  `daban` varchar(255) NOT NULL DEFAULT 'no',
  `reset` int(11) DEFAULT NULL,
  `game` varchar(100) DEFAULT 'aovvn'
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Đang đổ dữ liệu cho bảng `keytab`
--

INSERT INTO `keytab` (`id`, `tenkey`, `ngaykichhoat`, `hansudung`, `may`, `code`, `trangthai`, `daban`, `reset`, `game`) VALUES
(1, 'AOV_VIPPRO', '2025-04-14 10:56:42', '2026-04-14 10:56:42', '[\"bf54c9ed-fd7f-3400-b17f-ae36c78f2150\"]', 'tranhuong', 'hoatdong', 'yes', 0, 'aovvn'),
(3, 'aovvn_lT86T5vRA9', '2025-04-29 02:42:23', '2033-07-16 02:42:23', '[\"b1983100-ab9b-3df3-9ced-d1e78148f931\"]', 'tranhuong', 'hoatdong', 'yes', 0, 'aovvn'),
(4, 'aovvn_NUEbd0gtUF', '', '3000', '[\"Empty\"]', 'tranhuong', 'dangcho', 'no', 0, 'aovvn');

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `lichsu`
--

CREATE TABLE `lichsu` (
  `id` int(11) NOT NULL,
  `hoatdong` varchar(255) NOT NULL,
  `thoigian` varchar(255) NOT NULL,
  `ten` varchar(255) NOT NULL,
  `code` varchar(255) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Đang đổ dữ liệu cho bảng `lichsu`
--

INSERT INTO `lichsu` (`id`, `hoatdong`, `thoigian`, `ten`, `code`) VALUES
(136, 'Reset Key', '2025-05-24 02:14:32', 'AOV_VIPPRO', 'tranhuong');

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `login_history`
--

CREATE TABLE `login_history` (
  `id` int(11) NOT NULL,
  `key_id` varchar(255) DEFAULT NULL,
  `key_value` varchar(255) NOT NULL,
  `device_info` text DEFAULT NULL,
  `device_model` varchar(255) DEFAULT NULL,
  `android_version` varchar(50) DEFAULT NULL,
  `build_version` varchar(500) DEFAULT NULL,
  `game_code` varchar(50) DEFAULT NULL,
  `mod_version` varchar(50) DEFAULT NULL,
  `mod_title` varchar(255) DEFAULT NULL,
  `user_ip` varchar(50) DEFAULT NULL,
  `login_time` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `login_statistics`
--

CREATE TABLE `login_statistics` (
  `id` int(11) NOT NULL,
  `total_logins` int(11) NOT NULL DEFAULT 0,
  `logins_24h` int(11) NOT NULL DEFAULT 0,
  `logins_7d` int(11) NOT NULL DEFAULT 0,
  `updated_24h` timestamp NULL DEFAULT NULL,
  `updated_7d` timestamp NULL DEFAULT NULL,
  `last_updated` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `mod_app_mapping`
--

CREATE TABLE `mod_app_mapping` (
  `id` int(11) NOT NULL,
  `version_config_id` int(11) NOT NULL,
  `app_identifier` varchar(100) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Đang đổ dữ liệu cho bảng `mod_app_mapping`
--

INSERT INTO `mod_app_mapping` (`id`, `version_config_id`, `app_identifier`) VALUES
(52, 13, 'aovvn_2904'),
(53, 13, 'aovvn_3004');

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `version_config`
--

CREATE TABLE `version_config` (
  `id` int(11) NOT NULL,
  `version` varchar(50) NOT NULL,
  `apk_md5` varchar(255) NOT NULL,
  `signature` varchar(255) NOT NULL,
  `game_code` varchar(50) NOT NULL,
  `lib_name` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `safety` enum('safe','unsafe') DEFAULT 'safe',
  `safety_message` text DEFAULT NULL,
  `icon` varchar(50) DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL,
  `status` enum('ready','not_ready') DEFAULT 'not_ready',
  `file_path` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Đang đổ dữ liệu cho bảng `version_config`
--

INSERT INTO `version_config` (`id`, `version`, `apk_md5`, `signature`, `game_code`, `lib_name`, `description`, `safety`, `safety_message`, `icon`, `title`, `status`, `file_path`, `is_active`, `created_at`, `updated_at`) VALUES
(13, 'VN64-2904', 'ffc491772b7983fb7461fea145ad1f0b', 'e8:9b:15:8e:4b:cf:98:8e:bd:09:eb:83:f5:37:8e:87', 'aovvn', 'libVN.so', 'Chống Tố Cáo + Hack Map - Cam Xa + AIM Skill - Auto Bổ Trợ', 'safe', 'Ok An Toàn', '🅅🄸🄿', 'Mod Chống Tố', 'ready', 'files/libVN.so', 1, '2025-04-28 17:53:08', '2025-05-23 19:14:00');

-- --------------------------------------------------------

--
-- Cấu trúc bảng cho bảng `websetting`
--

CREATE TABLE `websetting` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `icon` varchar(255) NOT NULL,
  `des` varchar(255) NOT NULL,
  `keyw` varchar(255) NOT NULL,
  `anhbia` varchar(255) NOT NULL,
  `trangthai` varchar(255) NOT NULL
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_general_ci;

--
-- Đang đổ dữ liệu cho bảng `websetting`
--

INSERT INTO `websetting` (`id`, `title`, `icon`, `des`, `keyw`, `anhbia`, `trangthai`) VALUES
(1, '0944127046|Admin Panel ', 'https://shopapk.online/assets/img/favicon/favicon.ico', 'Admin Panel Mod Menu And Lua; One Device One Key', 'Admin Panel, Mod Menu, Login Menu, Login Lua', 'https://shopapk.online/public/images/page.png', 'hoatdong');

--
-- Chỉ mục cho các bảng đã đổ
--

--
-- Chỉ mục cho bảng `admin`
--
ALTER TABLE `admin`
  ADD PRIMARY KEY (`id`);

--
-- Chỉ mục cho bảng `keytab`
--
ALTER TABLE `keytab`
  ADD PRIMARY KEY (`id`);

--
-- Chỉ mục cho bảng `lichsu`
--
ALTER TABLE `lichsu`
  ADD PRIMARY KEY (`id`);

--
-- Chỉ mục cho bảng `login_history`
--
ALTER TABLE `login_history`
  ADD PRIMARY KEY (`id`);

--
-- Chỉ mục cho bảng `login_statistics`
--
ALTER TABLE `login_statistics`
  ADD PRIMARY KEY (`id`);

--
-- Chỉ mục cho bảng `mod_app_mapping`
--
ALTER TABLE `mod_app_mapping`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `version_app` (`version_config_id`,`app_identifier`);

--
-- Chỉ mục cho bảng `version_config`
--
ALTER TABLE `version_config`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `version_game_code` (`version`,`game_code`);

--
-- Chỉ mục cho bảng `websetting`
--
ALTER TABLE `websetting`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT cho các bảng đã đổ
--

--
-- AUTO_INCREMENT cho bảng `admin`
--
ALTER TABLE `admin`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=47;

--
-- AUTO_INCREMENT cho bảng `keytab`
--
ALTER TABLE `keytab`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT cho bảng `lichsu`
--
ALTER TABLE `lichsu`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=137;

--
-- AUTO_INCREMENT cho bảng `login_history`
--
ALTER TABLE `login_history`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `login_statistics`
--
ALTER TABLE `login_statistics`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT cho bảng `mod_app_mapping`
--
ALTER TABLE `mod_app_mapping`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=54;

--
-- AUTO_INCREMENT cho bảng `version_config`
--
ALTER TABLE `version_config`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT cho bảng `websetting`
--
ALTER TABLE `websetting`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- Các ràng buộc cho các bảng đã đổ
--

--
-- Các ràng buộc cho bảng `mod_app_mapping`
--
ALTER TABLE `mod_app_mapping`
  ADD CONSTRAINT `fk_version_config` FOREIGN KEY (`version_config_id`) REFERENCES `version_config` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
