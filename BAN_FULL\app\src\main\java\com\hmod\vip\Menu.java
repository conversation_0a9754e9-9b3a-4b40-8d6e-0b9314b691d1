package com.hmod.vip;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.PixelFormat;
import android.graphics.PorterDuff;
import android.graphics.Typeface;
import android.graphics.drawable.GradientDrawable;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.text.Html;
import android.text.InputFilter;
import android.text.InputType;
import android.text.TextUtils;
import android.text.method.DigitsKeyListener;
import android.util.Base64;
import android.util.DisplayMetrics;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.view.animation.AccelerateInterpolator;
import android.view.animation.Animation;
import android.view.animation.DecelerateInterpolator;
import android.view.animation.OvershootInterpolator;
import android.view.animation.ScaleAnimation;
import android.view.animation.TranslateAnimation;
import android.view.inputmethod.InputMethodManager;
import android.webkit.WebView;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.RelativeLayout;
import android.widget.ScrollView;
import android.widget.SeekBar;
import android.widget.Spinner;
import android.widget.Switch;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.ProgressBar;
import android.content.ClipboardManager;
import android.content.ClipData;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.Map;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Paint.Style;
import android.graphics.Paint.Cap;

import static android.view.ViewGroup.LayoutParams.WRAP_CONTENT;
import static android.view.ViewGroup.LayoutParams.MATCH_PARENT;
import static android.widget.RelativeLayout.ALIGN_PARENT_LEFT;
import static android.widget.RelativeLayout.ALIGN_PARENT_RIGHT;

import org.json.JSONObject;
import android.os.Looper;
import android.content.SharedPreferences;
import java.util.Set;

// Tạo lớp hỗ trợ animation kiểu IT hiện đại
class ITAnimations {

    // Thời gian cho các animation
    private static final int DURATION_SHORT = 250;
    private static final int DURATION_MEDIUM = 350;
    private static final int DURATION_LONG = 500;
    
    // Animation cho fade in mượt mà
    public static void fadeIn(final View view) {
        if (view.getVisibility() != View.VISIBLE) {
            view.setAlpha(0f);
            view.setVisibility(View.VISIBLE);
            view.animate()
                .alpha(1f)
                .setDuration(DURATION_SHORT)
                .setInterpolator(new DecelerateInterpolator(1.5f))
                .start();
        }
    }

    // Animation cho fade out mượt mà
    public static void fadeOut(final View view, final int endVisibility) {
        if (view.getVisibility() == View.VISIBLE) {
            view.animate()
                .alpha(0f)
                .setDuration(DURATION_SHORT)
                .setInterpolator(new AccelerateInterpolator(1.5f))
                .withEndAction(new Runnable() {
                    @Override
                    public void run() {
                        view.setVisibility(endVisibility);
                    }
                })
                .start();
        }
    }

    // Animation chọn tab có thêm hiệu ứng bounce
    public static void selectTab(final View view) {
        view.animate()
            .scaleX(0.95f)
            .scaleY(0.95f)
            .setDuration(100)
            .setInterpolator(new DecelerateInterpolator())
            .withEndAction(new Runnable() {
                @Override
                public void run() {
                    view.animate()
                        .scaleX(1f)
                        .scaleY(1f)
                        .setDuration(200)
                        .setInterpolator(new OvershootInterpolator(1.5f))
                        .start();
                }
            })
            .start();
    }

    // Hiệu ứng slide lên từ dưới lên
    public static void slideUp(View view) {
        if (view.getVisibility() != View.VISIBLE) {
            view.setVisibility(View.VISIBLE);
            TranslateAnimation animate = new TranslateAnimation(
                0, 0, view.getHeight(), 0);
            animate.setDuration(DURATION_MEDIUM);
            animate.setInterpolator(new DecelerateInterpolator(1.5f));
            view.startAnimation(animate);
        }
    }

    // Hiệu ứng slide xuống
    public static void slideDown(final View view) {
        if (view.getVisibility() == View.VISIBLE) {
            TranslateAnimation animate = new TranslateAnimation(
                0, 0, 0, view.getHeight());
            animate.setDuration(DURATION_MEDIUM);
            animate.setInterpolator(new AccelerateInterpolator(1.5f));
            animate.setAnimationListener(new Animation.AnimationListener() {
                    @Override
                    public void onAnimationStart(Animation animation) {}

                    @Override
                    public void onAnimationEnd(Animation animation) {
                        view.setVisibility(View.GONE);
                    }

                    @Override
                    public void onAnimationRepeat(Animation animation) {}
                });
            view.startAnimation(animate);
        }
    }

    // Hiệu ứng nút chuyển trạng thái ON/OFF
    public static void toggleButtonAnimation(final Button button, final boolean isOn, final GradientDrawable onBg, final GradientDrawable offBg) {
        button.animate()
            .scaleX(0.95f)
            .scaleY(0.95f)
            .setDuration(50)
            .withEndAction(new Runnable() {
                @Override
                public void run() {
                    // Đổi background và text
                    button.setBackground(isOn ? onBg : offBg);
                    button.animate()
                        .scaleX(1f)
                        .scaleY(1f)
                        .setDuration(150)
                        .setInterpolator(new OvershootInterpolator(1.2f))
                        .start();
                }
            })
            .start();
    }
    
    // Hiệu ứng ripple khi nhấn
    public static void buttonPress(final View view) {
        view.animate()
            .scaleX(0.97f)
            .scaleY(0.97f)
            .alpha(0.9f)
            .setDuration(100)
            .setInterpolator(new DecelerateInterpolator())
            .withEndAction(new Runnable() {
                @Override
                public void run() {
                    view.animate()
                        .scaleX(1f)
                        .scaleY(1f)
                        .alpha(1f)
                        .setDuration(100)
                        .setInterpolator(new DecelerateInterpolator())
                        .start();
                }
            })
            .start();
    }
}

public class Menu {
    // Constants
    public static final String TAG = "Mod_Menu";
    private final String USER = "USER";

    // UI Colors - Tech Style
    int TEXT_COLOR = Color.parseColor("#20BDFF"); // Cyan blue
    int TEXT_COLOR_2 = Color.parseColor("#FFFFFF"); // White text
    int BTN_COLOR = Color.parseColor("#4776E6"); // Button color
    int MENU_BG_COLOR = Color.parseColor("#0F2027"); // Dark background
    int MENU_FEATURE_BG_COLOR = Color.parseColor("#203A43"); // Feature background
    int MENU_WIDTH = 260; // Default value, will be recalculated
    int MENU_HEIGHT = 200; // Default value, will be recalculated
    int DUAL_MENU_WIDTH = 500; // Default value, will be recalculated
    int DUAL_MENU_HEIGHT = 300; // Default value, will be recalculated
    int POS_X = 0;
    int POS_Y = 100;

    // Menu size control
    float SCREEN_RATIO_WIDTH = 0.6f; // Width ratio to screen
    float SCREEN_RATIO_HEIGHT = 0.8f; // Height ratio to screen
    int screenWidth; // Actual screen width
    int screenHeight; // Actual screen height

    // Tab weights
    float CATEGORY_WEIGHT = 0.25f;
    float FEATURES_WEIGHT = 0.75f;

    // Style - Tech Theme
    float MENU_CORNER = 10f; // Corner radius
    int ICON_SIZE = 45;
    float ICON_ALPHA = 0.85f; // Icon visibility
    int ToggleON = Color.GREEN;
    int ToggleOFF = Color.RED;
    int BtnON = Color.parseColor("#11998e"); // Button ON color
    int BtnOFF = Color.parseColor("#f85032"); // Button OFF color
    int CategoryBG = Color.parseColor("#1E2832"); // Category background
    int SeekBarColor = Color.parseColor("#4776E6"); // SeekBar color
    int SeekBarProgressColor = Color.parseColor("#20BDFF"); // SeekBar progress color
    int CheckBoxColor = Color.parseColor("#4776E6"); // CheckBox color
    int RadioColor = Color.parseColor("#4776E6"); // Radio color
    String NumberTxtColor = "#20BDFF"; // Number text color

    // UI Components
    RelativeLayout mCollapsed, mRootContainer;
    LinearLayout mExpanded, mCollapse;
    LinearLayout mCategoriesLayout, mFeaturesLayout;
    WindowManager mWindowManager;
    WindowManager.LayoutParams vmParams;
    ImageView startimage;
    FrameLayout rootFrame;
    ScrollView categoryScrollView, featuresScrollView;
    boolean stopChecking, overlayRequired;
    Context getContext;

    // Menu state
    Map<String, String> tabToCategory;
    String currentCategory = "";
    TextView title, subTitle, categoryTitle, featuresTitle;
    public String key;
    private Prefs prefs;

    private String keyExpiryInfo = "";
    
    // Native methods
    native void Init(Context context, TextView title, TextView subTitle);
    public native String Check(Context mContext, String userKey);
    native String Icon();
    native String IconWebViewData();
    native String[] GetFeatureList();
    native String[] SettingsList();
    native boolean IsGameLibLoaded();
    native String[] GetCategoryList();

    // Other fields
    public JSONObject data;
    public String l;

    public Menu(Context context) {
        getContext = context;
        Preferences.context = context;

        // Get screen dimensions and calculate menu size
        calculateMenuSize(context);

        // Initialize UI
        initializeUI(context);

        // Initialize tab mapping
        initializeTabMapping();

        // Add custom settings category
        addCustomSettingsCategory();

        // Initialize preferences
        prefs = Prefs.with(getContext);
        key = prefs.read(USER, "");

        // Initialize menu
        Init(context, title, subTitle);
    }

    private void calculateMenuSize(Context context) {
        // Get actual screen dimensions
        DisplayMetrics displayMetrics = context.getResources().getDisplayMetrics();
        screenWidth = displayMetrics.widthPixels;
        screenHeight = displayMetrics.heightPixels;

        // Calculate menu size with ratio
        DUAL_MENU_WIDTH = (int)(screenWidth * SCREEN_RATIO_WIDTH);
        DUAL_MENU_HEIGHT = (int)(screenHeight * SCREEN_RATIO_HEIGHT);

        // Ensure menu is not too small or too large
        if (DUAL_MENU_WIDTH < 300) DUAL_MENU_WIDTH = 300;
        if (DUAL_MENU_HEIGHT < 200) DUAL_MENU_HEIGHT = 200;

        // Ensure menu does not exceed screen size
        if (DUAL_MENU_WIDTH > screenWidth - 10) {
            DUAL_MENU_WIDTH = screenWidth - 10;
        }
        if (DUAL_MENU_HEIGHT > screenHeight - 10) {
            DUAL_MENU_HEIGHT = screenHeight - 10;
        }

        // Default position in center of screen
        POS_X = (screenWidth - DUAL_MENU_WIDTH) / 2;
        POS_Y = (screenHeight - DUAL_MENU_HEIGHT) / 2;

        Log.d(TAG, "Screen size: " + screenWidth + "x" + screenHeight);
        Log.d(TAG, "Menu size: " + DUAL_MENU_WIDTH + "x" + DUAL_MENU_HEIGHT);
    }

    // Add custom settings category
    private void addCustomSettingsCategory() {
        // Check if custom settings category already exists
        if (tabToCategory != null && tabToCategory.containsValue("Tùy Chỉnh")) {
            return; // Already exists, no need to add
        }

        // Create new Map if none exists
        if (tabToCategory == null) {
            tabToCategory = new LinkedHashMap<>();
        }

        // Find the highest existing tab index
        int maxTabIndex = 0;
        for (String key : tabToCategory.keySet()) {
            if (key.startsWith("tab")) {
                try {
                    int index = Integer.parseInt(key.substring(3));
                    if (index > maxTabIndex) {
                        maxTabIndex = index;
                    }
                } catch (NumberFormatException e) {
                    // Skip if not a number
                }
            }
        }

        // Create new key for custom settings category
        String newTabKey = "tab" + (maxTabIndex + 1);

        // Add custom settings category
        tabToCategory.put(newTabKey, "Tùy Chỉnh");

        Log.d(TAG, "Added Custom Settings category with key: " + newTabKey);
    }
    
    private void createCustomSettings(final LinearLayout layout) {
    // Xóa tất cả view hiện tại
    layout.removeAllViews();

    // Tạo ScrollView để đảm bảo toàn bộ nội dung có thể cuộn được
    final ScrollView contentScrollView = new ScrollView(getContext);
    contentScrollView.setLayoutParams(new LinearLayout.LayoutParams(MATCH_PARENT, MATCH_PARENT));
    
    // Container chứa tất cả nội dung
    LinearLayout contentLayout = new LinearLayout(getContext);
    contentLayout.setOrientation(LinearLayout.VERTICAL);
    contentLayout.setLayoutParams(new LinearLayout.LayoutParams(MATCH_PARENT, WRAP_CONTENT));

    // Phần thông tin KEY
    Category(contentLayout, "Thông tin KEY");

    // Hiển thị thông tin hạn sử dụng KEY
    if (!keyExpiryInfo.isEmpty()) {
        TextView keyInfoView = new TextView(getContext);
        keyInfoView.setText(Html.fromHtml(keyExpiryInfo));
        keyInfoView.setTextColor(TEXT_COLOR);
        keyInfoView.setGravity(Gravity.CENTER);
        keyInfoView.setPadding(20, 10, 20, 15);

        // Tạo background cho key info
        GradientDrawable keyInfoBg = new GradientDrawable();
        keyInfoBg.setCornerRadius(8);
        keyInfoBg.setColor(Color.parseColor("#1A2A35"));
        keyInfoBg.setStroke(1, Color.parseColor("#20BDFF"));
        keyInfoView.setBackground(keyInfoBg);

        LinearLayout.LayoutParams keyInfoParams = new LinearLayout.LayoutParams(MATCH_PARENT, WRAP_CONTENT);
        keyInfoParams.setMargins(10, 5, 10, 15);
        keyInfoView.setLayoutParams(keyInfoParams);

        contentLayout.addView(keyInfoView);
    }

    // Thêm nút đăng xuất
    Button logoutButton = new Button(getContext);
    LinearLayout.LayoutParams logoutBtnParams = new LinearLayout.LayoutParams(MATCH_PARENT, WRAP_CONTENT);
    logoutBtnParams.setMargins(10, 5, 10, 20); // Tăng margin dưới
    logoutButton.setLayoutParams(logoutBtnParams);
    logoutButton.setText("ĐĂNG XUẤT KEY");
    logoutButton.setTextColor(Color.WHITE);
    logoutButton.setAllCaps(true); // Chữ hoa
    logoutButton.setTextSize(14);
    
    // Gradient cho nút đăng xuất
    GradientDrawable logoutBtnGradient = new GradientDrawable();
    logoutBtnGradient.setColor(Color.parseColor("#f85032"));
    logoutBtnGradient.setCornerRadius(10f);
    logoutButton.setBackground(logoutBtnGradient);

    // Hiệu ứng khi nhấn
    logoutButton.setOnTouchListener(new View.OnTouchListener() {
        @Override
        public boolean onTouch(View v, MotionEvent event) {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    v.setAlpha(0.7f);
                    break;
                case MotionEvent.ACTION_UP:
                case MotionEvent.ACTION_CANCEL:
                    v.setAlpha(1f);
                    break;
            }
            return false;
        }
    });

    // Xử lý đăng xuất
    logoutButton.setOnClickListener(new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            // Hiển thị dialog xác nhận
            AlertDialog.Builder alertDialog = new AlertDialog.Builder(getContext);
            alertDialog.setTitle("Xác nhận đăng xuất");
            alertDialog.setMessage("Bạn có chắc chắn muốn đăng xuất không?");

            alertDialog.setPositiveButton("Đồng ý", new DialogInterface.OnClickListener() {
                public void onClick(DialogInterface dialog, int which) {
                    performLogout();
                }
            });

            alertDialog.setNegativeButton("Hủy", new DialogInterface.OnClickListener() {
                public void onClick(DialogInterface dialog, int which) {
                    dialog.cancel();
                }
            });

            // Hiển thị dialog
            if (overlayRequired) {
                AlertDialog dialog = alertDialog.create();
                dialog.getWindow().setType(Build.VERSION.SDK_INT >= 26 ? 2038 : 2002);
                dialog.show();

                // Đặt màu cho nút
                Button positiveButton = dialog.getButton(DialogInterface.BUTTON_POSITIVE);
                Button negativeButton = dialog.getButton(DialogInterface.BUTTON_NEGATIVE);
                positiveButton.setTextColor(Color.parseColor("#f85032"));
                negativeButton.setTextColor(Color.parseColor("#8E8E93"));
            } else {
                alertDialog.show();
            }
        }
    });

    contentLayout.addView(logoutButton);

    // Phần tùy chỉnh kích thước menu
    Category(contentLayout, "TÙY CHỈNH KÍCH THƯỚC MENU");

    // Hiển thị kích thước hiện tại
    final TextView sizeInfoText = new TextView(getContext);
    sizeInfoText.setText("Kích thước hiện tại: " + DUAL_MENU_WIDTH + "x" + DUAL_MENU_HEIGHT);
    sizeInfoText.setTextColor(TEXT_COLOR_2);
    sizeInfoText.setGravity(Gravity.CENTER);
    sizeInfoText.setPadding(0, 10, 0, 10);
    contentLayout.addView(sizeInfoText);

    // Hiển thị tỷ lệ hiện tại
    final TextView percentText = new TextView(getContext);
    int currentPercent = (int)(SCREEN_RATIO_WIDTH * 100);
    percentText.setText("Tỷ lệ chiều rộng: " + currentPercent + "%");
    percentText.setTextColor(TEXT_COLOR_2);
    percentText.setGravity(Gravity.CENTER);
    percentText.setPadding(0, 0, 0, 10);
    contentLayout.addView(percentText);

    // Tạo container cho slider
    LinearLayout sliderContainer = new LinearLayout(getContext);
    sliderContainer.setOrientation(LinearLayout.VERTICAL);
    sliderContainer.setPadding(20, 0, 20, 10);
    contentLayout.addView(sliderContainer);

    // Tạo slider với phạm vi từ 50% đến 95%
    final SeekBar ratioSeekBar = new SeekBar(getContext);
    ratioSeekBar.setMax(95 - 50); // 50% đến 95%

    // Đặt giá trị hiện tại
    int progress = (int)((SCREEN_RATIO_WIDTH * 100) - 50);
    if (progress < 0) progress = 0;
    if (progress > 45) progress = 45;
    ratioSeekBar.setProgress(progress);

    // Colors
    ratioSeekBar.getThumb().setColorFilter(SeekBarProgressColor, PorterDuff.Mode.SRC_ATOP);
    ratioSeekBar.getProgressDrawable().setColorFilter(SeekBarColor, PorterDuff.Mode.SRC_ATOP);
    sliderContainer.addView(ratioSeekBar);

    // Biến tạm lưu giá trị trong quá trình kéo
    final int[] tempWidth = new int[1];
    final int[] tempHeight = new int[1];

    // Xử lý sự kiện kéo slider
    ratioSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
        @Override
        public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
            if (!fromUser) return;

            // Tính tỷ lệ từ 50% đến 95%
            float widthRatio = (progress + 50) / 100f;

            // Cập nhật text hiển thị tỷ lệ
            int percentValue = (int)(widthRatio * 100);
            percentText.setText("Tỷ lệ chiều rộng: " + percentValue + "%");

            // Tỷ lệ chiều cao = 4/3 của chiều rộng
            float heightRatio = widthRatio * (4f/3f);
            if (heightRatio > 0.95f) heightRatio = 0.95f;

            // Tính toán kích thước mới
            tempWidth[0] = (int)(screenWidth * widthRatio);
            tempHeight[0] = (int)(screenHeight * heightRatio);

            // Đảm bảo giới hạn kích thước
            if (tempWidth[0] < 300) tempWidth[0] = 300;
            if (tempHeight[0] < 200) tempHeight[0] = 200;
            if (tempWidth[0] > screenWidth - 10) tempWidth[0] = screenWidth - 10;
            if (tempHeight[0] > screenHeight - 10) tempHeight[0] = screenHeight - 10;

            // Cập nhật text thông tin kích thước
            sizeInfoText.setText("Kích thước hiện tại: " + tempWidth[0] + "x" + tempHeight[0]);
        }

        @Override
        public void onStartTrackingTouch(SeekBar seekBar) {
            tempWidth[0] = DUAL_MENU_WIDTH;
            tempHeight[0] = DUAL_MENU_HEIGHT;
        }

        @Override
        public void onStopTrackingTouch(SeekBar seekBar) {
            // Áp dụng kích thước mới
            float widthRatio = (seekBar.getProgress() + 50) / 100f;
            float heightRatio = widthRatio * (4f/3f);
            if (heightRatio > 0.95f) heightRatio = 0.95f;

            SCREEN_RATIO_WIDTH = widthRatio;
            SCREEN_RATIO_HEIGHT = heightRatio;
            
            DUAL_MENU_WIDTH = tempWidth[0];
            DUAL_MENU_HEIGHT = tempHeight[0];

            // Cập nhật kích thước menu
            setExactSize(DUAL_MENU_WIDTH, DUAL_MENU_HEIGHT);
            
            // Đảm bảo text hiển thị chính xác
            sizeInfoText.setText("Kích thước hiện tại: " + DUAL_MENU_WIDTH + "x" + DUAL_MENU_HEIGHT);

            // Đảm bảo ScrollView được cập nhật đúng
            contentScrollView.post(new Runnable() {
                @Override
                public void run() {
                    contentScrollView.fullScroll(View.FOCUS_DOWN);
                    contentScrollView.invalidate();
                }
            });
        }
    });

    // Nút đặt lại kích thước mặc định
    Button resetButton = new Button(getContext);
    LinearLayout.LayoutParams btnParams = new LinearLayout.LayoutParams(MATCH_PARENT, WRAP_CONTENT);
    btnParams.setMargins(20, 20, 20, 20);
    resetButton.setLayoutParams(btnParams);
    resetButton.setText("ĐẶT LẠI KÍCH THƯỚC MẶC ĐỊNH");
    resetButton.setTextColor(Color.WHITE);
    resetButton.setAllCaps(true); // Chữ hoa
    resetButton.setTextSize(14);
    
    // Gradient cho nút reset
    GradientDrawable buttonGradient = new GradientDrawable(GradientDrawable.Orientation.LEFT_RIGHT, 
                                     new int[] {Color.parseColor("#4776E6"), Color.parseColor("#8E54E9")});
    buttonGradient.setCornerRadius(10f);
    resetButton.setBackground(buttonGradient);
    
    // Xử lý reset
    resetButton.setOnClickListener(new View.OnClickListener() {
        @Override
        public void onClick(final View v) {
            // Hiệu ứng khi nhấn
            v.animate()
                .scaleX(0.95f)
                .scaleY(0.95f)
                .setDuration(100)
                .withEndAction(new Runnable() {
                    @Override
                    public void run() {
                        v.animate()
                            .scaleX(1.0f)
                            .scaleY(1.0f)
                            .setDuration(150)
                            .setInterpolator(new OvershootInterpolator(1.5f))
                            .start();
                        
                        // Reset về tỷ lệ mặc định
                        SCREEN_RATIO_WIDTH = 0.6f;
                        SCREEN_RATIO_HEIGHT = 0.8f;

                        // Tính toán lại kích thước và cập nhật
                        calculateMenuSize(getContext);
                        setExactSize(DUAL_MENU_WIDTH, DUAL_MENU_HEIGHT);

                        // Cập nhật UI
                        createCustomSettings(layout);
                        Toast.makeText(getContext, "Đã đặt lại kích thước mặc định", Toast.LENGTH_SHORT).show();
                    }
                })
                .start();
        }
    });
    contentLayout.addView(resetButton);

    // Thêm lưu ý ở cuối cùng
    TextView noteText = new TextView(getContext);
    noteText.setText(Html.fromHtml("<i>Lưu ý: Thay đổi sẽ được áp dụng ngay lập tức. Kéo thanh trượt để điều chỉnh từ 50% đến 95% kích thước màn hình.</i>"));
    noteText.setTextColor(TEXT_COLOR_2);
    noteText.setTextSize(12);
    noteText.setPadding(20, 10, 20, 30); // Tăng padding dưới
    noteText.setGravity(Gravity.CENTER);
    contentLayout.addView(noteText);

    // Thêm contentLayout vào ScrollView
    contentScrollView.addView(contentLayout);
    
    // Thêm ScrollView vào layout chính
    layout.addView(contentScrollView);
}
    
    private void createRatioSlider(LinearLayout layout) {
    LinearLayout sliderContainer = new LinearLayout(getContext);
    sliderContainer.setOrientation(LinearLayout.VERTICAL);
    sliderContainer.setPadding(20, 0, 20, 10);

    // Tạo TextView hiển thị kích thước hiện tại
    final TextView sizeInfoText = new TextView(getContext);
    sizeInfoText.setText("Kích thước hiện tại: " + DUAL_MENU_WIDTH + "x" + DUAL_MENU_HEIGHT);
    sizeInfoText.setTextColor(TEXT_COLOR_2);
    sizeInfoText.setPadding(0, 10, 0, 10);
    sliderContainer.addView(sizeInfoText);

    // Tạo TextView hiển thị phần trăm
    final TextView percentText = new TextView(getContext);
    int currentPercent = (int)(SCREEN_RATIO_WIDTH * 100);
    percentText.setText("Tỷ lệ chiều rộng: " + currentPercent + "%");
    percentText.setTextColor(TEXT_COLOR_2);
    percentText.setPadding(0, 0, 0, 10);
    sliderContainer.addView(percentText);

    // Tạo slider với phạm vi từ 50% đến 95%
    final SeekBar ratioSeekBar = new SeekBar(getContext);
    ratioSeekBar.setMax(95 - 50); // Từ 50% đến 95%

    // Đặt giá trị hiện tại
    int progress = (int)((SCREEN_RATIO_WIDTH * 100) - 50);
    // Đảm bảo giá trị nằm trong phạm vi hợp lệ
    if (progress < 0) progress = 0;
    if (progress > 45) progress = 45; // 95 - 50 = 45 là tối đa
    ratioSeekBar.setProgress(progress);

    // Màu sắc IT-style
    ratioSeekBar.getThumb().setColorFilter(SeekBarProgressColor, PorterDuff.Mode.SRC_ATOP);
    ratioSeekBar.getProgressDrawable().setColorFilter(SeekBarColor, PorterDuff.Mode.SRC_ATOP);

    // Biến tạm để lưu kích thước trong quá trình kéo
    final int[] tempWidth = new int[1];
    final int[] tempHeight = new int[1];

    // Xử lý sự kiện thay đổi
    ratioSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
        @Override
        public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
            if (!fromUser) return;

            // Tính toán tỷ lệ phần trăm từ 50% đến 95%
            float widthRatio = (progress + 50) / 100f; // 50% đến 95%

            // Cập nhật text hiển thị phần trăm
            int percentValue = (int)(widthRatio * 100);
            percentText.setText("Tỷ lệ chiều rộng: " + percentValue + "%");

            // Tỷ lệ chiều cao = 4/3 của tỷ lệ chiều rộng
            float heightRatio = widthRatio * (4f/3f);

            // Đảm bảo heightRatio không vượt quá 0.95 (95%)
            if (heightRatio > 0.95f) heightRatio = 0.95f;

            // Tính toán kích thước mới với tỷ lệ đã cho
            tempWidth[0] = (int)(screenWidth * widthRatio);
            tempHeight[0] = (int)(screenHeight * heightRatio);

            // Đảm bảo kích thước không quá nhỏ
            if (tempWidth[0] < 300) tempWidth[0] = 300;
            if (tempHeight[0] < 200) tempHeight[0] = 200;

            // Đảm bảo kích thước không vượt quá màn hình
            if (tempWidth[0] > screenWidth - 10) tempWidth[0] = screenWidth - 10;
            if (tempHeight[0] > screenHeight - 10) tempHeight[0] = screenHeight - 10;

            // Cập nhật text thông tin kích thước
            sizeInfoText.setText("Kích thước hiện tại: " + tempWidth[0] + "x" + tempHeight[0]);
        }

        @Override
        public void onStartTrackingTouch(SeekBar seekBar) {
            // Lưu kích thước ban đầu để phòng người dùng hủy
            tempWidth[0] = DUAL_MENU_WIDTH;
            tempHeight[0] = DUAL_MENU_HEIGHT;
        }

        @Override
        public void onStopTrackingTouch(SeekBar seekBar) {
            // Khi kéo xong, áp dụng kích thước mới
            float widthRatio = (seekBar.getProgress() + 50) / 100f; // 50% đến 95%
            float heightRatio = widthRatio * (4f/3f);

            // Đảm bảo heightRatio không vượt quá 0.95 (95%)
            if (heightRatio > 0.95f) heightRatio = 0.95f;

            // Cập nhật tỷ lệ thực tế cho cả hai chiều
            SCREEN_RATIO_WIDTH = widthRatio;
            SCREEN_RATIO_HEIGHT = heightRatio;

            // Áp dụng kích thước mới
            DUAL_MENU_WIDTH = tempWidth[0];
            DUAL_MENU_HEIGHT = tempHeight[0];

            // Cập nhật UI với kích thước mới
            setExactSize(DUAL_MENU_WIDTH, DUAL_MENU_HEIGHT);

            // Đảm bảo text hiển thị kích thước chính xác
            sizeInfoText.setText("Kích thước hiện tại: " + DUAL_MENU_WIDTH + "x" + DUAL_MENU_HEIGHT);
            
            // Cập nhật lại layout sau khi thay đổi kích thước
            new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    // Tính chiều cao khả dụng mới
                    int titleAreaHeight = 0;
                    if (mExpanded != null) {
                        for (int i = 0; i < mExpanded.getChildCount(); i++) {
                            View child = mExpanded.getChildAt(i);
                            if (child instanceof TextView || (i == 0 && child instanceof RelativeLayout)) {
                                titleAreaHeight += child.getHeight();
                            }
                        }
                    }
                    
                    // Thêm padding
                    titleAreaHeight += dp(15);
                    
                    // Tính toán chiều cao mới cho ScrollView
                    int availableHeight = DUAL_MENU_HEIGHT - titleAreaHeight;
                    if (availableHeight < dp(100)) availableHeight = dp(100);
                    
                    // Cập nhật ScrollView
                    if (categoryScrollView != null) {
                        ViewGroup.LayoutParams params = categoryScrollView.getLayoutParams();
                        if (params != null) {
                            params.height = availableHeight;
                            categoryScrollView.setLayoutParams(params);
                        }
                    }
                    
                    if (featuresScrollView != null) {
                        ViewGroup.LayoutParams params = featuresScrollView.getLayoutParams();
                        if (params != null) {
                            params.height = availableHeight;
                            featuresScrollView.setLayoutParams(params);
                        }
                    }
                    
                    // Yêu cầu vẽ lại layout
                    if (mExpanded != null) {
                        mExpanded.requestLayout();
                        
                        // Cập nhật lại nội dung phần Tùy Chỉnh
                        if (currentCategory != null && currentCategory.equals("Tùy Chỉnh")) {
                            createCustomSettings(mFeaturesLayout);
                        }
                    }
                }
            }, 100);  // Delay nhỏ để đảm bảo kích thước mới đã được áp dụng
        }
    });

    sliderContainer.addView(ratioSeekBar);
    layout.addView(sliderContainer);
/*
    // Thêm text hướng dẫn kéo
    TextView instructionText = new TextView(getContext);
    instructionText.setText("Kéo để điều chỉnh từ 50% đến 95%");
    instructionText.setTextColor(Color.parseColor("#8A8A8E"));
    instructionText.setTextSize(12);
    instructionText.setPadding(20, 5, 20, 10);
    instructionText.setGravity(Gravity.CENTER);
    layout.addView(instructionText);
    */
    // Thêm hiệu ứng highlight cho SeekBar khi đang điều chỉnh
    ratioSeekBar.setOnTouchListener(new View.OnTouchListener() {
        @Override
        public boolean onTouch(View v, MotionEvent event) {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    // Hiệu ứng khi bắt đầu chạm
                    v.setScaleX(1.05f);
                    v.setScaleY(1.2f);
                    break;
                    
                case MotionEvent.ACTION_UP:
                case MotionEvent.ACTION_CANCEL:
                    // Hiệu ứng khi kết thúc chạm
                    v.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .setDuration(150)
                        .start();
                    break;
            }
            // Trả về false để không chặn sự kiện SeekBar
            return false;
        }
    });
}

    private void initializeUI(Context context) {
        // Setup root views
        rootFrame = new FrameLayout(context);
        rootFrame.setLayoutParams(new FrameLayout.LayoutParams(DUAL_MENU_WIDTH, DUAL_MENU_HEIGHT));
        rootFrame.setOnTouchListener(onTouchListener());

        mRootContainer = new RelativeLayout(context);
        mRootContainer.setLayoutParams(new RelativeLayout.LayoutParams(DUAL_MENU_WIDTH, DUAL_MENU_HEIGHT));

        // Setup collapsed view (icon)
        mCollapsed = new RelativeLayout(context);
        int iconSize = dp(ICON_SIZE);
        RelativeLayout.LayoutParams collapsedParams = new RelativeLayout.LayoutParams(iconSize, iconSize);
        mCollapsed.setLayoutParams(collapsedParams);
        mCollapsed.setVisibility(View.VISIBLE);
        mCollapsed.setAlpha(ICON_ALPHA);

        // Setup expanded view (menu)
        mExpanded = new LinearLayout(context);
        mExpanded.setVisibility(View.GONE);
        mExpanded.setBackgroundColor(MENU_BG_COLOR);
        mExpanded.setOrientation(LinearLayout.VERTICAL);
        mExpanded.setPadding(3, 3, 3, 3);
        mExpanded.setLayoutParams(new LinearLayout.LayoutParams(DUAL_MENU_WIDTH, DUAL_MENU_HEIGHT));

        // Style the menu - Tech style with dark theme
        GradientDrawable gdMenuBody = new GradientDrawable();
        gdMenuBody.setCornerRadius(MENU_CORNER);
        gdMenuBody.setColor(MENU_BG_COLOR);
        gdMenuBody.setStroke(2, Color.parseColor("#20BDFF")); // Cyan border
        mExpanded.setBackground(gdMenuBody);

        // Create icon
        setupIcon(context);

        // Create title area
        setupTitleArea(context);

        // Setup dual tab layout
        setupDualTabLayout(context);

        // Setup buttons - Place at the end
        setupActionButtons(context);

        // Add views to containers
        mRootContainer.addView(mCollapsed);
        mRootContainer.addView(mExpanded);

        if (IconWebViewData() != null) {
            WebView wView = createIconWebView(context);
            mCollapsed.addView(wView);
        } else {
            mCollapsed.addView(startimage);
        }
    }

    private void setupIcon(Context context) {
        startimage = new ImageView(context);

        // Important: Set LayoutParams with EXACT size, don't use WRAP_CONTENT
        int iconSize = dp(ICON_SIZE);
        RelativeLayout.LayoutParams iconParams = new RelativeLayout.LayoutParams(iconSize, iconSize);

        // Set appropriate top margin
        iconParams.topMargin = 0; // Set to 0 to avoid distortion
        startimage.setLayoutParams(iconParams);

        // Set ScaleType to CENTER_INSIDE to ensure round icon
        startimage.setScaleType(ImageView.ScaleType.CENTER_INSIDE);

        // Load and display icon
        byte[] decode = Base64.decode(Icon(), 0);
        startimage.setImageBitmap(BitmapFactory.decodeByteArray(decode, 0, decode.length));

        // Set listener
        startimage.setOnTouchListener(onTouchListener());
        startimage.setOnClickListener(new View.OnClickListener() {
                public void onClick(View view) {
                    openMenu();
                }
            });
    }
    
    private void openMenu() {
    // Khôi phục kích thước menu gốc
    if (rootFrame.getTag() != null) {
        int[] savedSize = (int[]) rootFrame.getTag();
        vmParams.width = savedSize[0];
        vmParams.height = savedSize[1];
    } else {
        vmParams.width = DUAL_MENU_WIDTH;
        vmParams.height = DUAL_MENU_HEIGHT;
    }

    // Lưu vị trí icon hiện tại
    int currentIconX = vmParams.x;
    int currentIconY = vmParams.y;

    // Tính toán vị trí mới để đảm bảo menu hiển thị đầy đủ
    int newMenuX = currentIconX;
    int newMenuY = currentIconY;

    // Kiểm tra xem menu có vượt quá cạnh phải không
    if (currentIconX + vmParams.width > screenWidth) {
        newMenuX = screenWidth - vmParams.width;
    }

    // Kiểm tra xem menu có vượt quá cạnh dưới không
    if (currentIconY + vmParams.height > screenHeight) {
        newMenuY = screenHeight - vmParams.height;
    }

    // Đảm bảo không âm
    if (newMenuX < 0) newMenuX = 0;
    if (newMenuY < 0) newMenuY = 0;

    // Đặt vị trí mới
    vmParams.x = newMenuX;
    vmParams.y = newMenuY;

    // Cập nhật layout
    mWindowManager.updateViewLayout(rootFrame, vmParams);

    // Hiệu ứng khi mở menu
    mExpanded.setScaleX(0.95f);
    mExpanded.setScaleY(0.95f);
    mExpanded.setAlpha(0f);

    // Ẩn icon
    mCollapsed.setVisibility(View.GONE);

    // Hiển thị menu với hiệu ứng
    mExpanded.setVisibility(View.VISIBLE);
    mExpanded.animate()
        .scaleX(1f)
        .scaleY(1f)
        .alpha(1f)
        .setDuration(250)
        .setInterpolator(new DecelerateInterpolator(1.2f))
        .start();

    rootFrame.setOnTouchListener(onTouchListener());

    // Tự động điều chỉnh kích thước ScrollView sau khi menu hiển thị
    mExpanded.post(new Runnable() {
        @Override
        public void run() {
            // Tính toán không gian có sẵn cho nội dung
            int titleAreaHeight = 0;
            
            // Tìm và tính chiều cao của các thành phần chính
            for (int i = 0; i < mExpanded.getChildCount(); i++) {
                View child = mExpanded.getChildAt(i);
                if (child instanceof TextView || (i == 0 && child instanceof RelativeLayout)) {
                    titleAreaHeight += child.getHeight();
                }
            }
            
            // Thêm padding để đảm bảo khoảng cách hợp lý
            titleAreaHeight += dp(15);
            
            // Tính toán chiều cao khả dụng
            int availableHeight = vmParams.height - titleAreaHeight;
            
            // Đảm bảo chiều cao tối thiểu
            if (availableHeight < dp(100)) availableHeight = dp(100);
            
            // Cập nhật chiều cao cho cả hai ScrollView
            if (categoryScrollView != null) {
                ViewGroup.LayoutParams params = categoryScrollView.getLayoutParams();
                if (params != null) {
                    params.height = availableHeight;
                    categoryScrollView.setLayoutParams(params);
                }
            }

            if (featuresScrollView != null) {
                ViewGroup.LayoutParams params = featuresScrollView.getLayoutParams();
                if (params != null) {
                    params.height = availableHeight;
                    featuresScrollView.setLayoutParams(params);
                }
            }
        }
    });
    
    // Kiểm tra và cập nhật vị trí của các phần tử UI nếu cần
    new Handler().postDelayed(new Runnable() {
        @Override
        public void run() {
            // Đảm bảo các scroll view được vẽ chính xác
            if (categoryScrollView != null) {
                categoryScrollView.invalidate();
            }
            if (featuresScrollView != null) {
                featuresScrollView.invalidate();
            }
            
            // Làm mới nội dung hiển thị
            if (mExpanded != null) {
                mExpanded.requestLayout();
            }
        }
    }, 300); // Một chút delay để đảm bảo hiệu ứng đã hoàn thành

    Log.d(TAG, "Menu opened with size: " + vmParams.width + "x" + vmParams.height);
}

    private WebView createIconWebView(Context context) {
        WebView wView = new WebView(context);
        wView.setLayoutParams(new RelativeLayout.LayoutParams(WRAP_CONTENT, WRAP_CONTENT));
        int applyDimension = (int) TypedValue.applyDimension(1, ICON_SIZE, context.getResources().getDisplayMetrics());
        wView.getLayoutParams().height = applyDimension;
        wView.getLayoutParams().width = applyDimension;
        wView.loadData("<html>" +
                       "<head></head>" +
                       "<body style=\"margin: 0; padding: 0\">" +
                       "<img src=\"" + IconWebViewData() + "\" width=\"" + ICON_SIZE + "\" height=\"" + ICON_SIZE + "\" >" +
                       "</body>" +
                       "</html>", "text/html", "utf-8");
        wView.setBackgroundColor(0x00000000);
        wView.setAlpha(ICON_ALPHA);
        wView.getSettings().setAppCacheEnabled(true);
        wView.setOnTouchListener(onTouchListener());
        return wView;
    }

    private void setupTitleArea(Context context) {
    // Tech style title frame with glow effect
    RelativeLayout titleText = new RelativeLayout(context);
    titleText.setPadding(10, 10, 10, 10);
    titleText.setVerticalGravity(16);

    GradientDrawable titleFrame = new GradientDrawable();
    titleFrame.setColor(Color.parseColor("#17232D")); // Dark tech blue
    titleFrame.setCornerRadius(8);
    titleFrame.setStroke(1, Color.parseColor("#20BDFF")); // Cyan border
    titleText.setBackground(titleFrame);

    // Tạo title view
    title = new TextView(context);
    title.setTextColor(TEXT_COLOR); // Cyan text
    title.setTypeface(Typeface.create("sans-serif-medium", Typeface.BOLD));
    title.setTextSize(16.0f);
    title.setGravity(Gravity.CENTER);
    title.setPadding(20, 5, 20, 5);
    RelativeLayout.LayoutParams rl = new RelativeLayout.LayoutParams(WRAP_CONTENT, WRAP_CONTENT);
    rl.addRule(RelativeLayout.CENTER_HORIZONTAL);
    title.setLayoutParams(rl);

    // Tạo nút X ở bên phải
    ImageView closeButton = new ImageView(context);
    int buttonSize = dp(20);
    RelativeLayout.LayoutParams closeParams = new RelativeLayout.LayoutParams(buttonSize, buttonSize);
    closeParams.addRule(RelativeLayout.ALIGN_PARENT_RIGHT);
    closeParams.addRule(RelativeLayout.CENTER_VERTICAL);
    closeParams.rightMargin = dp(10);
    closeButton.setLayoutParams(closeParams);
    
    // Tạo hình tròn với dấu X
    Bitmap bitmap = Bitmap.createBitmap(buttonSize, buttonSize, Bitmap.Config.ARGB_8888);
    Canvas canvas = new Canvas(bitmap);
    
    // Vẽ hình tròn
    Paint circlePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    circlePaint.setColor(Color.parseColor("#203A43"));
    circlePaint.setStyle(Paint.Style.FILL);
    canvas.drawCircle(buttonSize/2, buttonSize/2, buttonSize/2 - dp(1), circlePaint);
    
    // Vẽ viền tròn
    Paint strokePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    strokePaint.setColor(Color.parseColor("#20BDFF"));
    strokePaint.setStyle(Paint.Style.STROKE);
    strokePaint.setStrokeWidth(dp(1));
    canvas.drawCircle(buttonSize/2, buttonSize/2, buttonSize/2 - dp(1), strokePaint);
    
    // Vẽ dấu X
    Paint linePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    linePaint.setColor(Color.parseColor("#20BDFF"));
    linePaint.setStyle(Paint.Style.STROKE);
    linePaint.setStrokeWidth(dp(2.5f));
    linePaint.setStrokeCap(Paint.Cap.ROUND);
    
    int padding = dp(7);
    canvas.drawLine(padding, padding, buttonSize - padding, buttonSize - padding, linePaint);
    canvas.drawLine(buttonSize - padding, padding, padding, buttonSize - padding, linePaint);
    
    closeButton.setImageBitmap(bitmap);
    
    // Thêm hiệu ứng khi nhấn
    closeButton.setOnTouchListener(new View.OnTouchListener() {
        @Override
        public boolean onTouch(View v, MotionEvent event) {
            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    v.animate()
                        .scaleX(0.85f)
                        .scaleY(0.85f)
                        .alpha(0.7f)
                        .setDuration(100)
                        .start();
                    break;
                case MotionEvent.ACTION_UP:
                    v.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .alpha(1.0f)
                        .setDuration(100)
                        .start();
                    // Khi nhấn xong thì ẩn menu
                    hideMenu();
                    break;
                case MotionEvent.ACTION_CANCEL:
                    v.animate()
                        .scaleX(1.0f)
                        .scaleY(1.0f)
                        .alpha(1.0f)
                        .setDuration(100)
                        .start();
                    break;
            }
            return true;
        }
    });

    subTitle = new TextView(context);
    subTitle.setEllipsize(TextUtils.TruncateAt.MARQUEE);
    subTitle.setMarqueeRepeatLimit(-1);
    subTitle.setSingleLine(true);
    subTitle.setSelected(true);
    subTitle.setTextColor(TEXT_COLOR); // Cyan text
    subTitle.setTypeface(Typeface.create("sans-serif", Typeface.NORMAL));
    subTitle.setTextSize(11.0f);
    subTitle.setGravity(Gravity.CENTER);
    subTitle.setPadding(0, 0, 0, 5);

    titleText.addView(title);
    titleText.addView(closeButton); // Thêm nút đóng vào layout tiêu đề
    mExpanded.addView(titleText);
    mExpanded.addView(subTitle);
}

    private int dp(float f) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, f, getContext.getResources().getDisplayMetrics());
    }

    private void setupDualTabLayout(Context context) {
    // Main horizontal layout for dual tabs
    LinearLayout dualTabLayout = new LinearLayout(context);
    dualTabLayout.setOrientation(LinearLayout.HORIZONTAL);
    dualTabLayout.setLayoutParams(new LinearLayout.LayoutParams(MATCH_PARENT, 0, 1.0f)); // Change weight to use available space

    // Category tab (left side)
    LinearLayout categoryTabLayout = new LinearLayout(context);
    categoryTabLayout.setOrientation(LinearLayout.VERTICAL);
    LinearLayout.LayoutParams categoryParams = new LinearLayout.LayoutParams(0, MATCH_PARENT, CATEGORY_WEIGHT);
    categoryParams.setMargins(10, 0, 5, 0);
    categoryTabLayout.setLayoutParams(categoryParams);

    // Category header - Tech style
    categoryTitle = new TextView(context);
    categoryTitle.setText("Categories");
    categoryTitle.setTextColor(TEXT_COLOR); // Cyan text
    categoryTitle.setTypeface(Typeface.create("sans-serif-medium", Typeface.BOLD));
    categoryTitle.setTextSize(15.0f);
    categoryTitle.setGravity(Gravity.CENTER);
    categoryTitle.setPadding(10, 10, 10, 10);

    // Tech-style header with dark background
    GradientDrawable headerBg = new GradientDrawable();
    headerBg.setColor(CategoryBG); // Dark tech grey
    headerBg.setCornerRadii(new float[]{MENU_CORNER, MENU_CORNER, MENU_CORNER, MENU_CORNER, 0, 0, 0, 0});
    categoryTitle.setBackground(headerBg);

    // Categories scroll view - Tech style - Tận dụng tối đa không gian
    categoryScrollView = new ScrollView(context);
    categoryScrollView.setLayoutParams(new LinearLayout.LayoutParams(MATCH_PARENT, MATCH_PARENT));
    categoryScrollView.setBackgroundColor(MENU_FEATURE_BG_COLOR); // Dark tech background
    
    // Thêm hiệu ứng overscroll mượt mà
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
        categoryScrollView.setOverScrollMode(View.OVER_SCROLL_IF_CONTENT_SCROLLS);
        categoryScrollView.setNestedScrollingEnabled(true);
    }

    mCategoriesLayout = new LinearLayout(context);
    mCategoriesLayout.setOrientation(LinearLayout.VERTICAL);
    mCategoriesLayout.setPadding(0, 0, 0, 8); // Padding

    // Features tab (right side)
    LinearLayout featuresTabLayout = new LinearLayout(context);
    featuresTabLayout.setOrientation(LinearLayout.VERTICAL);
    LinearLayout.LayoutParams featuresParams = new LinearLayout.LayoutParams(0, MATCH_PARENT, FEATURES_WEIGHT);
    featuresParams.setMargins(5, 0, 10, 0);
    featuresTabLayout.setLayoutParams(featuresParams);

    // Features header - Tech style
    featuresTitle = new TextView(context);
    featuresTitle.setText("Features");
    featuresTitle.setTextColor(TEXT_COLOR); // Cyan text
    featuresTitle.setTypeface(Typeface.create("sans-serif-medium", Typeface.BOLD));
    featuresTitle.setTextSize(15.0f);
    featuresTitle.setGravity(Gravity.CENTER);
    featuresTitle.setPadding(10, 10, 10, 10);

    // Tech-style header with dark background
    GradientDrawable featureHeaderBg = new GradientDrawable();
    featureHeaderBg.setColor(CategoryBG); // Dark tech grey
    featureHeaderBg.setCornerRadii(new float[]{MENU_CORNER, MENU_CORNER, MENU_CORNER, MENU_CORNER, 0, 0, 0, 0});
    featuresTitle.setBackground(featureHeaderBg);

    // Features scroll view - Tech style - Tận dụng tối đa không gian
    featuresScrollView = new ScrollView(context);
    featuresScrollView.setLayoutParams(new LinearLayout.LayoutParams(MATCH_PARENT, MATCH_PARENT));
    featuresScrollView.setBackgroundColor(MENU_FEATURE_BG_COLOR); // Dark tech background
    
    // Thêm hiệu ứng overscroll mượt mà
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
        featuresScrollView.setOverScrollMode(View.OVER_SCROLL_IF_CONTENT_SCROLLS);
        featuresScrollView.setNestedScrollingEnabled(true);
    }

    mFeaturesLayout = new LinearLayout(context);
    mFeaturesLayout.setOrientation(LinearLayout.VERTICAL);
    mFeaturesLayout.setPadding(0, 0, 0, 0); // Giảm padding dưới cùng xuống 0

    // Assemble views
    categoryScrollView.addView(mCategoriesLayout);
    featuresScrollView.addView(mFeaturesLayout);

    categoryTabLayout.addView(categoryTitle);
    categoryTabLayout.addView(categoryScrollView);

    featuresTabLayout.addView(featuresTitle);
    featuresTabLayout.addView(featuresScrollView);

    // Add divider - Tech style (subtle glow effect)
    View divider = new View(context);
    LinearLayout.LayoutParams dividerParams = new LinearLayout.LayoutParams(1, MATCH_PARENT);
    divider.setLayoutParams(dividerParams);
    divider.setBackgroundColor(Color.parseColor("#20BDFF")); // Cyan divider

    // Add all to dual tab layout
    dualTabLayout.addView(categoryTabLayout);
    dualTabLayout.addView(divider);
    dualTabLayout.addView(featuresTabLayout);

    // Add to mExpanded
    mExpanded.addView(dualTabLayout);
    
    // Xử lý sau khi đã thêm vào giao diện
    mExpanded.post(new Runnable() {
        @Override
        public void run() {
            // Điều chỉnh chiều cao của ScrollView khi layout đã hoàn tất
            if (mExpanded.getHeight() > 0) {
                int headerHeight = categoryTitle.getHeight() + dp(5); // Tính chiều cao phần header + margin
                int availableHeight = mExpanded.getHeight() - headerHeight - title.getHeight() - subTitle.getHeight() - dp(20);
                
                // Đảm bảo chiều cao tối thiểu
                if (availableHeight < dp(100)) availableHeight = dp(100);
                
                // Cập nhật chiều cao cho cả hai ScrollView
                ViewGroup.LayoutParams catParams = categoryScrollView.getLayoutParams();
                if (catParams != null) {
                    catParams.height = availableHeight;
                    categoryScrollView.setLayoutParams(catParams);
                }
                
                ViewGroup.LayoutParams featParams = featuresScrollView.getLayoutParams();
                if (featParams != null) {
                    featParams.height = availableHeight;
                    featuresScrollView.setLayoutParams(featParams);
                }
            }
        }
    });
}
    
    private void setupActionButtons(Context context) {
    }

    private void hideMenu() {
    // Lưu kích thước menu hiện tại
    final int savedWidth = vmParams.width;
    final int savedHeight = vmParams.height;

    // Hiệu ứng thu nhỏ và mờ dần khi đóng
    mExpanded.animate()
        .alpha(0f)
        .scaleX(0.9f)
        .scaleY(0.9f)
        .setDuration(250)
        .setInterpolator(new AccelerateInterpolator(1.2f))
        .withEndAction(new Runnable() {
            @Override
            public void run() {
                mExpanded.setVisibility(View.GONE);

                // Hiển thị lại icon với hiệu ứng fade in
                mCollapsed.setAlpha(0f);
                mCollapsed.setVisibility(View.VISIBLE);
                mCollapsed.animate()
                    .alpha(ICON_ALPHA)
                    .setDuration(200)
                    .start();

                // Thay đổi kích thước rootFrame để chỉ hiển thị icon
                int iconSize = dp(ICON_SIZE);
                vmParams.width = iconSize;
                vmParams.height = iconSize;

                // Cập nhật layout
                mWindowManager.updateViewLayout(rootFrame, vmParams);

                // Đảm bảo icon hiển thị hoàn toàn trong rootFrame
                RelativeLayout.LayoutParams iconParams = (RelativeLayout.LayoutParams) startimage.getLayoutParams();
                iconParams.topMargin = 0; // Reset top margin của icon
                startimage.setLayoutParams(iconParams);

                // Căn chỉnh lại container của icon
                RelativeLayout.LayoutParams collapsedParams = (RelativeLayout.LayoutParams) mCollapsed.getLayoutParams();
                collapsedParams.width = iconSize;
                collapsedParams.height = iconSize;
                mCollapsed.setLayoutParams(collapsedParams);

                // Lưu kích thước gốc để khôi phục khi mở menu
                rootFrame.setTag(new int[]{savedWidth, savedHeight});

                // Cập nhật OnTouchListener chỉ cho icon
                mCollapsed.setOnTouchListener(onTouchListener());
                startimage.setOnTouchListener(onTouchListener());
            }
        })
        .start();
}

    private void initializeTabMapping() {
        String[] categories = GetCategoryList(); // Get list from C++

        tabToCategory = new LinkedHashMap<>();

        // If no categories from C++, leave empty
        if (categories == null || categories.length == 0) {
            // Don't create any default categories
            return;
        }

        // Process categories from C++
        for (int i = 0; i < categories.length; i++) {
            String category = categories[i];
            String tabKey = "tab" + (i + 1);
            tabToCategory.put(tabKey, category);
        }
    }

    private void createCategoryList() {
        if (mCategoriesLayout != null) {
            mCategoriesLayout.removeAllViews();

            // Check if there are any categories
            if (tabToCategory.isEmpty()) {
                TextView emptyText = new TextView(getContext);
                emptyText.setText("No categories found");
                emptyText.setTextColor(TEXT_COLOR_2);
                emptyText.setPadding(20, 20, 20, 20);
                emptyText.setGravity(Gravity.CENTER);
                mCategoriesLayout.addView(emptyText);
                return;
            }

            // Get ordered list of categories from tabToCategory
            List<String> orderedCategories = new ArrayList<>(tabToCategory.values());

            for (final String category : orderedCategories) {
                final Button categoryButton = new Button(getContext);

                // Tech style buttons - sleeker, more compact
                LinearLayout.LayoutParams buttonParams = new LinearLayout.LayoutParams(MATCH_PARENT, dp(40)); 
                buttonParams.setMargins(8, 4, 8, 4);

                categoryButton.setLayoutParams(buttonParams);
                categoryButton.setText(category);
                categoryButton.setTextColor(TEXT_COLOR_2);
                categoryButton.setAllCaps(false);  // Not all caps
                categoryButton.setTypeface(Typeface.create("sans-serif-medium", Typeface.NORMAL));
                categoryButton.setTextSize(13);
                categoryButton.setPadding(15, 5, 15, 5);

                // Make corners rounded for tech style
                final GradientDrawable buttonBg = new GradientDrawable();
                buttonBg.setCornerRadius(8);

                // Highlight selected category
                if (category.equals(currentCategory)) {
                    buttonBg.setColor(Color.parseColor("#1E88E5"));  // Blue highlight
                    categoryButton.setTextColor(TEXT_COLOR_2);  // White text for selected
                } else {
                    buttonBg.setColor(CategoryBG);  // Dark tech grey
                }
                categoryButton.setBackground(buttonBg);

                categoryButton.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            // Add animation when selecting category
                            ITAnimations.selectTab(categoryButton);

                            if (!category.equals(currentCategory)) {
                                currentCategory = category;
                                createCategoryList(); // Refresh buttons to show selection
                                loadFeaturesForCategory(category);
                            }
                        }
                    });

                mCategoriesLayout.addView(categoryButton);
            }
        }
    }

    // Load features for selected category with animation
    private void loadFeaturesForCategory(final String category) {
        if (mFeaturesLayout != null) {
            // Animation when changing tab
            mFeaturesLayout.setAlpha(1f);
            mFeaturesLayout.animate()
                .alpha(0f)
                .setDuration(150) // Fast animation
                .setInterpolator(new AccelerateInterpolator())
                .withEndAction(new Runnable() {
                    @Override
                    public void run() {
                        mFeaturesLayout.removeAllViews();

                        // Update title with fade effect
                        if (featuresTitle != null) {
                            featuresTitle.setText("" + category);
                            featuresTitle.setAlpha(0f);
                            featuresTitle.animate()
                                .alpha(1f)
                                .setDuration(200)
                                .setInterpolator(new DecelerateInterpolator())
                                .start();
                        }

                        // Check if this is custom settings category
                        if (category.equals("Tùy Chỉnh")) {
                            createCustomSettings(mFeaturesLayout);
                        } else {
                            // Get tab prefix for current category
                            String tabPrefix = null;
                            for (Map.Entry<String, String> entry : tabToCategory.entrySet()) {
                                if (entry.getValue().equals(category)) {
                                    tabPrefix = entry.getKey();
                                    break;
                                }
                            }

                            if (tabPrefix == null) {
                                TextView emptyText = new TextView(getContext);
                                emptyText.setText("Error: No matching tab found");
                                emptyText.setTextColor(TEXT_COLOR_2);
                                emptyText.setPadding(20, 20, 20, 20);
                                emptyText.setGravity(Gravity.CENTER);
                                mFeaturesLayout.addView(emptyText);
                            } else {
                                // Filter features by tab prefix
                                String[] allFeatures = GetFeatureList();
                                List<String> filteredFeatures = new ArrayList<>();

                                for (String feature : allFeatures) {
                                    if (feature.startsWith(tabPrefix + "_")) {
                                        filteredFeatures.add(feature);
                                    }
                                }

                                // Display features or empty message
                                if (filteredFeatures.isEmpty()) {
                                    TextView emptyText = new TextView(getContext);
                                    emptyText.setText("No features in this category");
                                    emptyText.setTextColor(TEXT_COLOR_2);
                                    emptyText.setPadding(20, 20, 20, 20);
                                    emptyText.setGravity(Gravity.CENTER);
                                    mFeaturesLayout.addView(emptyText);
                                } else {
                                    featureList(filteredFeatures.toArray(new String[0]), mFeaturesLayout);
                                }
                            }
                        }

                        // Fade in animation after content update
                        mFeaturesLayout.animate()
                            .alpha(1f)
                            .setDuration(200)
                            .setInterpolator(new DecelerateInterpolator())
                            .start();
                    }
                })
                .start();
        }
    }
    
public void setExactSize(final int width, final int height) {
    DUAL_MENU_WIDTH = width;
    DUAL_MENU_HEIGHT = height;

    // Sử dụng Handler để trì hoãn cập nhật UI
    new Handler().post(new Runnable() {
        @Override
        public void run() {
            // Cập nhật tham số layout cho rootFrame
            ViewGroup.LayoutParams rootParams = rootFrame.getLayoutParams();
            if (rootParams != null) {
                rootParams.width = width;
                rootParams.height = height;
                rootFrame.setLayoutParams(rootParams);
            }

            // Cập nhật tham số layout cho mRootContainer
            ViewGroup.LayoutParams containerParams = mRootContainer.getLayoutParams();
            if (containerParams != null) {
                containerParams.width = width;
                containerParams.height = height;
                mRootContainer.setLayoutParams(containerParams);
            }

            // Cập nhật tham số layout cho mExpanded
            ViewGroup.LayoutParams expandedParams = mExpanded.getLayoutParams();
            if (expandedParams != null) {
                expandedParams.width = width;
                expandedParams.height = height;
                mExpanded.setLayoutParams(expandedParams);
            }

            // Cập nhật WindowManager với kích thước mới
            if (mWindowManager != null && vmParams != null) {
                vmParams.width = width;
                vmParams.height = height;
                mWindowManager.updateViewLayout(rootFrame, vmParams);
            }

            // Đợi một chút để các thay đổi kích thước được áp dụng
            mExpanded.post(new Runnable() {
                @Override
                public void run() {
                    // Tính toán chiều cao cho các thành phần chính
                    int titleHeight = 0;
                    int subtitleHeight = 0;
                    int categoryHeaderHeight = 0;
                    int featureHeaderHeight = 0;

                    // Lấy chiều cao của tiêu đề và phụ đề
                    if (title != null) {
                        titleHeight = title.getHeight();
                    }
                    if (subTitle != null) {
                        subtitleHeight = subTitle.getHeight();
                    }
                    if (categoryTitle != null) {
                        categoryHeaderHeight = categoryTitle.getHeight();
                    }
                    if (featuresTitle != null) {
                        featureHeaderHeight = featuresTitle.getHeight();
                    }

                    // Tính toán chiều cao khả dụng cho ScrollView
                    // Đây là chiều cao tổng trừ đi các phần cố định
                    int headerHeight = Math.max(categoryHeaderHeight, featureHeaderHeight);
                    int availableHeight = height - titleHeight - subtitleHeight - headerHeight - dp(20);
                    
                    // Đảm bảo chiều cao tối thiểu cho ScrollView
                    if (availableHeight < dp(100)) {
                        availableHeight = dp(100);
                    }

                    // Cập nhật chiều cao cho CategoryScrollView
                    if (categoryScrollView != null) {
                        ViewGroup.LayoutParams params = categoryScrollView.getLayoutParams();
                        if (params != null) {
                            params.height = availableHeight;
                            categoryScrollView.setLayoutParams(params);
                        }
                    }

                    // Cập nhật chiều cao cho FeaturesScrollView
                    if (featuresScrollView != null) {
                        ViewGroup.LayoutParams params = featuresScrollView.getLayoutParams();
                        if (params != null) {
                            params.height = availableHeight;
                            featuresScrollView.setLayoutParams(params);
                        }
                    }

                    // Yêu cầu vẽ lại layout sau khi thay đổi kích thước
                    mExpanded.requestLayout();

                    // Kiểm tra nếu đang ở tab Tùy Chỉnh thì cập nhật lại nội dung
                    if (currentCategory != null && currentCategory.equals("Tùy Chỉnh") && mFeaturesLayout != null) {
                        createCustomSettings(mFeaturesLayout);
                    }
                    
                    // Thêm đợi lần 2 để đảm bảo mọi thứ được cập nhật hoàn toàn
                    new Handler().postDelayed(new Runnable() {
                        @Override
                        public void run() {
                            // Làm mới các scroll view
                            if (categoryScrollView != null) {
                                categoryScrollView.invalidate();
                            }
                            if (featuresScrollView != null) {
                                featuresScrollView.invalidate();
                            }
                            
                            // Yêu cầu vẽ lại một lần nữa
                            if (mExpanded != null) {
                                mExpanded.requestLayout();
                            }
                        }
                    }, 50);
                }
            });
            
            Log.d(TAG, "Menu size updated to: " + width + "x" + height);
        }
    });
}

    // Check if size is valid
    private boolean isSizeValid(int width, int height) {
        // Check if size is appropriate
        return width >= 300 && height >= 200 && width <= screenWidth && height <= screenHeight;
    }

    private void showLoginScreen() {
        // Hide header and category tab
        mCategoriesLayout.setVisibility(View.GONE);
        categoryScrollView.setVisibility(View.GONE);
        categoryTitle.setVisibility(View.GONE);

        // Hide features title as well
        featuresTitle.setVisibility(View.GONE);

        // Hide divider
        View divider = ((ViewGroup)featuresScrollView.getParent().getParent()).getChildAt(1);
        if (divider != null) {
            divider.setVisibility(View.GONE);
        }

        // Set new layout for features to occupy all space
        LinearLayout dualTabLayout = (LinearLayout)featuresScrollView.getParent().getParent();
        if (dualTabLayout != null) {
            // Change layout params to occupy full width
            LinearLayout featuresLayout = (LinearLayout)featuresScrollView.getParent();
            if (featuresLayout != null) {
                LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                    MATCH_PARENT, WRAP_CONTENT, 1.0f);
                params.setMargins(10, 0, 10, 0); // Even margins on both sides
                featuresLayout.setLayoutParams(params);
            }
        }

        // Set title to main menu title
        if (title != null) {
            title.setText("H-MOD");
            title.setTextColor(TEXT_COLOR); // Cyan text
        }

        // Clear subtitle
        if (subTitle != null) {
            subTitle.setText("");
        }

        // Clear old content and display login form
        mFeaturesLayout.removeAllViews();

        // Create login interface - Tech style
        InputLogin(mFeaturesLayout, 1, "Paste KEY here ...");
    }

    private String cleanupKey(String key) {
        if (key == null) return "";

        // Remove all whitespace, tabs, newlines
        return key.replaceAll("\\s+", "").trim();
    }

    private void showLoginLoadingState(final Button inputButton, final String rawKey) {
        // Clean KEY before using
        final String key = cleanupKey(rawKey);

        // Find subtitle TextView next to inputButton (next view)
        ViewGroup parent = (ViewGroup) inputButton.getParent().getParent();
        TextView subtitleText = null;
        LinearLayout loadingContainer = null;

        // Find subtitle and loading container
        for (int i = 0; i < parent.getChildCount(); i++) {
            View child = parent.getChildAt(i);
            if (child instanceof TextView) {
                subtitleText = (TextView) child;
            } else if (child instanceof LinearLayout && child.getVisibility() == View.GONE) {
                loadingContainer = (LinearLayout) child;
            }
        }

        // If not found, exit
        if (subtitleText == null || loadingContainer == null) return;

        // Hide input container
        final View inputContainer = (View) inputButton.getParent();
        final TextView finalSubtitleText = subtitleText;
        final LinearLayout finalLoadingContainer = loadingContainer;

        // Smooth transition to loading state
        inputContainer.animate()
            .alpha(0f)
            .setDuration(150)
            .withEndAction(new Runnable() {
                @Override
                public void run() {
                    inputContainer.setVisibility(View.GONE);
                    finalSubtitleText.animate()
                        .alpha(0f)
                        .setDuration(100)
                        .withEndAction(new Runnable() {
                            @Override
                            public void run() {
                                finalSubtitleText.setVisibility(View.GONE);

                                // Fade in animation for loading container
                                finalLoadingContainer.setAlpha(0f);
                                finalLoadingContainer.setVisibility(View.VISIBLE);
                                finalLoadingContainer.animate()
                                    .alpha(1f)
                                    .setDuration(200)
                                    .start();
                            }
                        })
                        .start();
                }
            })
            .start();

        // Process login with pasted key
        new Handler().postDelayed(new Runnable() {
                @Override
                public void run() {
                    try {
                        if(Login(getContext, key)) {
                            // Animation when login successful
                            finalLoadingContainer.animate()
                                .alpha(0f)
                                .setDuration(200)
                                .withEndAction(new Runnable() {
                                    @Override
                                    public void run() {
                                        // Show category tab again after successful login
                                        mCategoriesLayout.setVisibility(View.VISIBLE);
                                        categoryScrollView.setVisibility(View.VISIBLE);
                                        categoryTitle.setVisibility(View.VISIBLE);

                                        // Restore original layout
                                        LinearLayout.LayoutParams categoryParams = new LinearLayout.LayoutParams(0, WRAP_CONTENT, CATEGORY_WEIGHT);
                                        categoryParams.setMargins(10, 0, 5, 0);
                                        ((View) categoryScrollView.getParent()).setLayoutParams(categoryParams);

                                        LinearLayout.LayoutParams featuresParams = new LinearLayout.LayoutParams(0, WRAP_CONTENT, FEATURES_WEIGHT);
                                        featuresParams.setMargins(5, 0, 10, 0);
                                   ((View) featuresScrollView.getParent()).setLayoutParams(featuresParams);

                                        // Display notification
                                        Toast.makeText(getContext, "Login successful!", Toast.LENGTH_SHORT).show();
                                    }
                                })
                                .start();
                        } else {
                            // Animation when login failed
                            finalLoadingContainer.animate()
                                .alpha(0f)
                                .setDuration(200)
                                .withEndAction(new Runnable() {
                                    @Override
                                    public void run() {
                                        finalLoadingContainer.setVisibility(View.GONE);

                                        // Show button and subtitle again
                                        inputContainer.setVisibility(View.VISIBLE);
                                        inputContainer.setAlpha(0f);
                                        inputContainer.animate()
                                            .alpha(1f)
                                            .setDuration(200)
                                            .start();

                                        finalSubtitleText.setVisibility(View.VISIBLE);
                                        finalSubtitleText.setAlpha(0f);
                                        finalSubtitleText.animate()
                                            .alpha(1f)
                                            .setDuration(200)
                                            .start();

                                        Toast.makeText(getContext, "Login failed, please check your credentials!", Toast.LENGTH_SHORT).show();
                                    }
                                })
                                .start();
                        }
                    } catch (Exception e) {
                        // Animation when error occurs
                        finalLoadingContainer.animate()
                            .alpha(0f)
                            .setDuration(200)
                            .withEndAction(new Runnable() {
                                @Override
                                public void run() {
                                    finalLoadingContainer.setVisibility(View.GONE);

                                    // Show button and subtitle again
                                    inputContainer.setVisibility(View.VISIBLE);
                                    inputContainer.setAlpha(0f);
                                    inputContainer.animate()
                                        .alpha(1f)
                                        .setDuration(200)
                                        .start();

                                    finalSubtitleText.setVisibility(View.VISIBLE);
                                    finalSubtitleText.setAlpha(0f);
                                    finalSubtitleText.animate()
                                        .alpha(1f)
                                        .setDuration(200)
                                        .start();

                                    Toast.makeText(getContext, "Error occurred. Please try again.", Toast.LENGTH_SHORT).show();
                                }
                            })
                            .start();
                    }
                }
            }, 1500);
    }
    
    private void performLogout() {
        // Check for null and safety conditions
        if (getContext == null || prefs == null) {
            Log.e(TAG, "Cannot perform logout: Context or Preferences is null");
            return;
        }

        // Perform logout even if no view is present
        try {
            // Clear key and settings
            prefs.clear();
            key = "";
            keyExpiryInfo = ""; // Clear expiry info

            // Clear all categories
            if (tabToCategory != null) {
                tabToCategory.clear();
            }

            // Switch to login screen with animation
            new Handler(Looper.getMainLooper()).post(new Runnable() {
                    @Override
                    public void run() {
                        // Fade out animation if view exists
                        if (mFeaturesLayout != null && mFeaturesLayout.getChildCount() > 0) {
                            View currentView = mFeaturesLayout.getChildAt(0);
                            currentView.animate()
                                .alpha(0f)
                                .setDuration(250)
                                .setInterpolator(new AccelerateInterpolator())
                                .withEndAction(new Runnable() {
                                    @Override
                                    public void run() {
                                        // Show login screen
                                        showLoginScreen();

                                        // Fade in animation for login screen
                                        if (mFeaturesLayout.getChildCount() > 0) {
                                            View loginView = mFeaturesLayout.getChildAt(0);
                                            loginView.setAlpha(0f);
                                            loginView.animate()
                                                .alpha(1f)
                                                .setDuration(300)
                                                .setInterpolator(new DecelerateInterpolator())
                                                .start();
                                        }
                                    }
                                })
                                .start();
                        } else {
                            // If no view, just call showLoginScreen
                            showLoginScreen();
                        }

                        // Notification
                        Toast.makeText(getContext, "Logged out successfully!", Toast.LENGTH_SHORT).show();
                    }
                });
        } catch (Exception e) {
            Log.e(TAG, "Error during logout", e);
            // Fallback if error occurs
            Toast.makeText(getContext, "Error during logout", Toast.LENGTH_SHORT).show();
        }
    }
    
    public void ShowMenu() {
        // Ensure mRootContainer is added to rootFrame
        if (rootFrame.getChildCount() == 0) {
            rootFrame.addView(mRootContainer);
        }

        // Always use full menu size
        vmParams.width = DUAL_MENU_WIDTH;
        vmParams.height = DUAL_MENU_HEIGHT;

        // Update WindowManager layout
        try {
            mWindowManager.updateViewLayout(rootFrame, vmParams);
        } catch (Exception e) {
            Log.e(TAG, "Error updating view layout: " + e.getMessage());
        }

        // Hide icon, show menu
        mCollapsed.setVisibility(View.GONE);
        mExpanded.setVisibility(View.VISIBLE);
        mExpanded.setAlpha(1f);
        mExpanded.setScaleX(1f);
        mExpanded.setScaleY(1f);

        // Check login status
        prefs = Prefs.with(getContext);
        key = prefs.read(USER, "");

        if (key.equals("")) {
            // If not logged in, display login screen
            showLoginScreen();
        } else {
            // Logged in, verify login
            if (Login(getContext, key)) {
                // Reset all feature settings, but keep key
                resetAllFeatureSettings();

                createCategoryList();

                if (!tabToCategory.isEmpty()) {
                    currentCategory = tabToCategory.values().iterator().next();
                    loadFeaturesForCategory(currentCategory);
                } else {
                    TextView emptyText = new TextView(getContext);
                    emptyText.setText("No categories from C++. Please update category list.");
                    emptyText.setTextColor(TEXT_COLOR_2);
                    emptyText.setPadding(20, 20, 20, 20);
                    emptyText.setGravity(Gravity.CENTER);
                    mFeaturesLayout.addView(emptyText);
                }
            } else {
                showLoginScreen();
            }
        }

        // Set touch listener
        rootFrame.setOnTouchListener(onTouchListener());
    }

    private void resetAllFeatureSettings() {
        try {
            SharedPreferences sharedPreferences = getContext.getSharedPreferences(
                getContext.getPackageName() + "_preferences",
                Context.MODE_PRIVATE
            );

            // Get all keys
            Set<String> allKeys = sharedPreferences.getAll().keySet();
            SharedPreferences.Editor editor = sharedPreferences.edit();

            // Remove all keys with "feature_" prefix
            for (String key : allKeys) {
                if (key.startsWith("feature_")) {
                    editor.remove(key);
                }
            }

            editor.apply();

            // Log reset message
            Log.d(TAG, "Reset all feature settings completed");
        } catch (Exception e) {
            Log.e(TAG, "Error resetting feature settings: " + e.getMessage());
        }
    }
    
    @SuppressLint("WrongConstant")
    public void SetWindowManagerWindowService() {
        int iparams = Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O ? 2038 : 2002;

        // When first opening app, only show icon with appropriate size
        int initialWidth = dp(ICON_SIZE);
        int initialHeight = dp(ICON_SIZE);

        vmParams = new WindowManager.LayoutParams(
            initialWidth,  // Only icon size
            initialHeight, // Only icon size
            iparams, 
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | 
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS | 
            WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN | 
            WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL, // Important - allows touch events to pass through
            PixelFormat.TRANSLUCENT
        );

        vmParams.gravity = Gravity.TOP | Gravity.LEFT;
        vmParams.x = POS_X;
        vmParams.y = POS_Y;

        // Ensure only icon is initially displayed
        mExpanded.setVisibility(View.GONE);
        mCollapsed.setVisibility(View.VISIBLE);

        // Set size of icon container
        RelativeLayout.LayoutParams collapsedParams = new RelativeLayout.LayoutParams(initialWidth, initialHeight);
        mCollapsed.setLayoutParams(collapsedParams);

        // Ensure touch listener is set for icon
        mCollapsed.setOnTouchListener(onTouchListener());
        startimage.setOnTouchListener(onTouchListener());

        mWindowManager = (WindowManager) getContext.getSystemService(getContext.WINDOW_SERVICE);
        mWindowManager.addView(rootFrame, vmParams);

        overlayRequired = true;

        // Save menu size to restore when opening
        rootFrame.setTag(new int[]{DUAL_MENU_WIDTH, DUAL_MENU_HEIGHT});
    }

    @SuppressLint("WrongConstant")
    public void SetWindowManagerActivity() {
        vmParams = new WindowManager.LayoutParams(
            DUAL_MENU_WIDTH,
            DUAL_MENU_HEIGHT,
            POS_X,
            POS_Y,
            WindowManager.LayoutParams.TYPE_APPLICATION,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
            WindowManager.LayoutParams.FLAG_LAYOUT_IN_OVERSCAN |
            WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN |
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS | 
            WindowManager.LayoutParams.FLAG_SPLIT_TOUCH,
            PixelFormat.TRANSPARENT
        );

        vmParams.gravity = Gravity.TOP | Gravity.LEFT;
        vmParams.x = POS_X;
        vmParams.y = POS_Y;

        mWindowManager = ((Activity) getContext).getWindowManager();
        mWindowManager.addView(rootFrame, vmParams);

        // Add log to check actual size
        rootFrame.post(new Runnable() {
                @Override
                public void run() {
                    Log.d(TAG, "Actual menu size (Activity): " + rootFrame.getWidth() + "x" + rootFrame.getHeight());
                    Log.d(TAG, "Requested size: " + DUAL_MENU_WIDTH + "x" + DUAL_MENU_HEIGHT);
                }
            });
    }

    private View.OnTouchListener onTouchListener() {
        return new View.OnTouchListener() {
            final View collapsedView = mCollapsed;
            final View expandedView = mExpanded;
            private float initialTouchX, initialTouchY;
            private int initialX, initialY;

            public boolean onTouch(View view, MotionEvent motionEvent) {
                // Critical issue: Only accept touch events if they are actually within the icon area
                if (isViewCollapsed()) {
                    // Get actual icon coordinates on screen
                    int[] iconLocation = new int[2];
                    if (startimage != null) {
                        startimage.getLocationOnScreen(iconLocation);
                    } else if (collapsedView != null) {
                        collapsedView.getLocationOnScreen(iconLocation);
                    } else {
                        // No icon to determine position, skip event
                        return false;
                    }

                    // Get icon size
                    int iconWidth = startimage != null ? startimage.getWidth() : dp(ICON_SIZE);
                    int iconHeight = startimage != null ? startimage.getHeight() : dp(ICON_SIZE);

                    // Touch point coordinates
                    float touchRawX = motionEvent.getRawX();
                    float touchRawY = motionEvent.getRawY();

                    // Check if touch point is within icon area
                    boolean touchInIconBounds = 
                        touchRawX >= iconLocation[0] && 
                        touchRawX <= iconLocation[0] + iconWidth &&
                        touchRawY >= iconLocation[1] && 
                        touchRawY <= iconLocation[1] + iconHeight;

                    // If not touching icon, skip event
                    if (!touchInIconBounds) {
                        return false;
                    }
                }

                DisplayMetrics displayMetrics = getContext.getResources().getDisplayMetrics();
                int screenWidth = displayMetrics.widthPixels;
                int screenHeight = displayMetrics.heightPixels;

                switch (motionEvent.getAction()) {
                    case MotionEvent.ACTION_DOWN:
                        initialX = vmParams.x;
                        initialY = vmParams.y;
                        initialTouchX = motionEvent.getRawX();
                        initialTouchY = motionEvent.getRawY();
                        return true;

                    case MotionEvent.ACTION_UP:
                        int rawX = (int) (motionEvent.getRawX() - initialTouchX);
                        int rawY = (int) (motionEvent.getRawY() - initialTouchY);
                        mExpanded.setAlpha(1f);
                        mCollapsed.setAlpha(ICON_ALPHA);

                        // Only open menu when clicking icon, not dragging
                        if (Math.abs(rawX) < 10 && Math.abs(rawY) < 10 && isViewCollapsed()) {
                            openMenu();
                        }
                        return true;

                    case MotionEvent.ACTION_MOVE:
                        mExpanded.setAlpha(0.5f);
                        mCollapsed.setAlpha(0.5f);

                        int newX = initialX + ((int) (motionEvent.getRawX() - initialTouchX));
                        int newY = initialY + ((int) (motionEvent.getRawY() - initialTouchY));

                        // Keep icon on screen
                        if (newX < 0) newX = 0;
                        if (newY < 0) newY = 0;
                        if (newX > screenWidth - dp(ICON_SIZE)) newX = screenWidth - dp(ICON_SIZE);
                        if (newY > screenHeight - dp(ICON_SIZE)) newY = screenHeight - dp(ICON_SIZE);

                        vmParams.x = newX;
                        vmParams.y = newY;
                        mWindowManager.updateViewLayout(rootFrame, vmParams);
                        return true;

                    default:
                        return false;
                }
            }
        };
    }

    private void InputLogin(LinearLayout linLayout, final int featNum, final String featName) {
        // Main container - Tech style clean
        final LinearLayout loginContainer = new LinearLayout(getContext);
        loginContainer.setOrientation(LinearLayout.VERTICAL);
        loginContainer.setGravity(Gravity.CENTER);
        loginContainer.setPadding(20, 20, 20, 20);
        loginContainer.setBackgroundColor(MENU_FEATURE_BG_COLOR);  // Dark tech background

        // Spacer
        View spaceView = new View(getContext);
        LinearLayout.LayoutParams spaceParams = new LinearLayout.LayoutParams(MATCH_PARENT, dp(40));
        spaceView.setLayoutParams(spaceParams);
        loginContainer.addView(spaceView);

        // Create container for input and paste button
        LinearLayout inputContainer = new LinearLayout(getContext);
        inputContainer.setOrientation(LinearLayout.VERTICAL);
        LinearLayout.LayoutParams inputContainerParams = new LinearLayout.LayoutParams(MATCH_PARENT, WRAP_CONTENT);
        inputContainerParams.setMargins(20, 10, 20, 10);
        inputContainer.setLayoutParams(inputContainerParams);

        // Login button - Tech style
        final Button inputButton = new Button(getContext);
        LinearLayout.LayoutParams btnParams = new LinearLayout.LayoutParams(MATCH_PARENT, dp(45));
        inputButton.setLayoutParams(btnParams);

        // Tech style button
        GradientDrawable btnGradient = new GradientDrawable();
        btnGradient.setColor(Color.parseColor("#203A43"));
        btnGradient.setStroke(1, TEXT_COLOR);  // Cyan border
        btnGradient.setCornerRadius(10);  // Tech rounded corners
        inputButton.setBackground(btnGradient);
        inputButton.setText("Paste KEY here ...");
        inputButton.setTextColor(TEXT_COLOR);  // Cyan text
        inputButton.setAllCaps(false);  // Not all caps
        inputButton.setTypeface(null, Typeface.NORMAL);  // Normal font weight
        inputContainer.addView(inputButton);

        // Add "Auto Paste KEY" button below
        Button pasteButton = new Button(getContext);
        LinearLayout.LayoutParams pasteButtonParams = new LinearLayout.LayoutParams(MATCH_PARENT, dp(45));
        pasteButtonParams.setMargins(0, dp(10), 0, 0);
        pasteButton.setLayoutParams(pasteButtonParams);
        pasteButton.setText("Auto Paste KEY");
        pasteButton.setTextColor(Color.WHITE);
        pasteButton.setAllCaps(false); // Not all caps

        // Style for Paste button - Tech style gradient button
        GradientDrawable pasteButtonBg = new GradientDrawable(GradientDrawable.Orientation.LEFT_RIGHT, 
            new int[] {Color.parseColor("#4776E6"), Color.parseColor("#8E54E9")});
        pasteButtonBg.setCornerRadius(10); // Tech rounded corners
        pasteButton.setBackground(pasteButtonBg);

        // Event when clicking Auto Paste KEY button
        pasteButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(final View v) {
                    // Get clipboard manager
                    ClipboardManager clipboard = (ClipboardManager) getContext.getSystemService(Context.CLIPBOARD_SERVICE);

                    // Check if there's content in clipboard
                    if (clipboard.hasPrimaryClip()) {
                        ClipData.Item item = clipboard.getPrimaryClip().getItemAt(0);
                        String pasteData = item.getText().toString();

                        // Clean KEY before using
                        String cleanedKey = cleanupKey(pasteData);

                        // Directly use cleaned key to login
                        showLoginLoadingState(inputButton, cleanedKey);

                        // Add light animation when paste is successful
                        v.animate()
                            .scaleX(0.95f)
                            .scaleY(0.95f)
                            .setDuration(100)
                            .withEndAction(new Runnable() {
                                @Override
                                public void run() {
                                    v.animate()
                                        .scaleX(1.05f)
                                        .scaleY(1.05f)
                                        .setDuration(150)
                                        .setInterpolator(new OvershootInterpolator(1.5f))
                                        .withEndAction(new Runnable() {
                                            @Override
                                            public void run() {
                                                v.animate()
                                                    .scaleX(1.0f)
                                                    .scaleY(1.0f)
                                                    .setDuration(100)
                                                    .start();
                                            }
                                        })
                                        .start();
                                }
                            })
                            .start();
                    } else {
                        Toast.makeText(getContext, "No content found to paste", Toast.LENGTH_SHORT).show();
                    }
                }
            });

        // Animation when clicking button
        pasteButton.setOnTouchListener(new View.OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    switch (event.getAction()) {
                        case MotionEvent.ACTION_DOWN:
                            v.animate()
                                .scaleX(0.95f)
                                .scaleY(0.95f)
                                .alpha(0.9f)
                                .setDuration(100)
                                .start();
                            break;
                        case MotionEvent.ACTION_UP:
                        case MotionEvent.ACTION_CANCEL:
                            v.animate()
                                .scaleX(1.0f)
                                .scaleY(1.0f)
                                .alpha(1.0f)
                                .setDuration(100)
                                .start();
                            break;
                    }
                    return false;
                }
            });

        inputContainer.addView(pasteButton);
        loginContainer.addView(inputContainer);

        // Subtitle - Tech style
        final TextView subtitleText = new TextView(getContext);
        subtitleText.setText("Note: Please paste KEY and login to MOD before logging into the Game!");
        subtitleText.setTextColor(Color.parseColor("#A5FECB"));  // Tech green text
        subtitleText.setTextSize(13);
        subtitleText.setGravity(Gravity.CENTER);
        subtitleText.setPadding(0, 20, 0, 0);
        loginContainer.addView(subtitleText);

        // Loading container - Tech style
        final LinearLayout loadingContainer = new LinearLayout(getContext);
        loadingContainer.setOrientation(LinearLayout.VERTICAL);
        loadingContainer.setGravity(Gravity.CENTER);
        loadingContainer.setVisibility(View.GONE);

        // Progress bar - Tech style
        final ProgressBar progressBar = new ProgressBar(getContext);
        progressBar.setIndeterminate(true);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            progressBar.getIndeterminateDrawable().setColorFilter(TEXT_COLOR, PorterDuff.Mode.SRC_IN);  // Cyan color
        }
        LinearLayout.LayoutParams progressParams = new LinearLayout.LayoutParams(dp(40), dp(40));
        progressParams.gravity = Gravity.CENTER;
        progressParams.setMargins(0, 10, 0, 10);
        progressBar.setLayoutParams(progressParams);
        loadingContainer.addView(progressBar);

        // Loading text - Tech style
        final TextView loadingText = new TextView(getContext);
        loadingText.setText("Verifying...");
        loadingText.setTextColor(TEXT_COLOR_2);  // White text
        loadingText.setTextSize(14);
        loadingText.setGravity(Gravity.CENTER);
        loadingText.setPadding(0, 0, 0, 10);
        loadingContainer.addView(loadingText);
        loginContainer.addView(loadingContainer);

        // Add bottom spacer for balance
        View spacerBottom = new View(getContext);
        LinearLayout.LayoutParams spacerBottomParams = new LinearLayout.LayoutParams(MATCH_PARENT, 0, 1.0f);
        spacerBottom.setLayoutParams(spacerBottomParams);
        loginContainer.addView(spacerBottom);

        // Click animation - Tech style
        inputButton.setOnTouchListener(new View.OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    switch (event.getAction()) {
                        case MotionEvent.ACTION_DOWN:
                            v.setAlpha(0.7f);
                            break;
                        case MotionEvent.ACTION_UP:
                        case MotionEvent.ACTION_CANCEL:
                            v.setAlpha(1f);
                            break;
                    }
                    return false;
                }
            });

        // Click handler
        inputButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    showLoginDialog(inputButton, subtitleText, loadingContainer, loginContainer);
                }
            });

        linLayout.addView(loginContainer);
    }

    private void showLoginDialog(final Button inputButton, final TextView subtitleText, 
                                 final LinearLayout loadingContainer, final LinearLayout loginContainer) {
        // Tech style alert dialog
        AlertDialog.Builder alertName = new AlertDialog.Builder(getContext);
        alertName.setTitle("Login");

        // Create layout for dialog
        LinearLayout dialogLayout = new LinearLayout(getContext);
        dialogLayout.setOrientation(LinearLayout.VERTICAL);
        dialogLayout.setPadding(dp(20), dp(10), dp(20), 0);

        // Custom EditText - Tech style
        final EditText editText = new EditText(getContext);
        editText.setHint("Your KEY will be displayed here...");
        editText.setHintTextColor(Color.parseColor("#8E8E93"));  // Placeholder color
        editText.setTextColor(TEXT_COLOR_2);  // White text color
        editText.setPadding(dp(20), dp(15), dp(20), dp(15));

        // Style EditText - Tech style
        GradientDrawable editTextBG = new GradientDrawable();
        editTextBG.setColor(Color.parseColor("#20283C"));  // Dark tech background
        editTextBG.setCornerRadius(8);  // Corner radius
        editTextBG.setStroke(1, Color.parseColor("#20BDFF"));  // Cyan border
        editText.setBackground(editTextBG);

        // Create "Paste KEY" button - Tech style
        Button pasteButton = new Button(getContext);
        LinearLayout.LayoutParams pasteButtonParams = new LinearLayout.LayoutParams(MATCH_PARENT, WRAP_CONTENT);
        pasteButtonParams.setMargins(0, dp(10), 0, dp(10));
        pasteButton.setLayoutParams(pasteButtonParams);
        pasteButton.setText("Paste KEY from clipboard");
        pasteButton.setTextColor(Color.WHITE);
        pasteButton.setAllCaps(false); // Not all caps

        // Style for Paste button - Tech style gradient
        GradientDrawable pasteButtonBg = new GradientDrawable(GradientDrawable.Orientation.LEFT_RIGHT, 
            new int[] {Color.parseColor("#4776E6"), Color.parseColor("#8E54E9")});
        pasteButtonBg.setCornerRadius(8); // Tech rounded corners
        pasteButton.setBackground(pasteButtonBg);

        // Event when clicking Paste KEY button
        pasteButton.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    // Get clipboard manager
                    ClipboardManager clipboard = (ClipboardManager) getContext.getSystemService(Context.CLIPBOARD_SERVICE);

                    // Check if there's content in clipboard
                    if (clipboard.hasPrimaryClip()) {
                        ClipData.Item item = clipboard.getPrimaryClip().getItemAt(0);
                        String pasteData = item.getText().toString();

                        // Clean KEY before displaying
                        String cleanedKey = cleanupKey(pasteData);

                        // Set cleaned content in editText
                        editText.setText(cleanedKey);

                        // Add light animation when paste is successful
                        editText.animate()
                            .scaleX(1.05f)
                            .scaleY(1.05f)
                            .setDuration(150)
                            .withEndAction(new Runnable() {
                                @Override
                                public void run() {
                                    editText.animate()
                                        .scaleX(1.0f)
                                        .scaleY(1.0f)
                                        .setDuration(150)
                                        .start();
                                }
                            })
                            .start();

                        Toast.makeText(getContext, "KEY pasted from clipboard", Toast.LENGTH_SHORT).show();
                    } else {
                        Toast.makeText(getContext, "No content found to paste", Toast.LENGTH_SHORT).show();
                    }
                }
            });

        // Animation when clicking button
        pasteButton.setOnTouchListener(new View.OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    switch (event.getAction()) {
                        case MotionEvent.ACTION_DOWN:
                            v.animate()
                                .scaleX(0.95f)
                                .scaleY(0.95f)
                                .alpha(0.9f)
                                .setDuration(100)
                                .start();
                            break;
                        case MotionEvent.ACTION_UP:
                        case MotionEvent.ACTION_CANCEL:
                            v.animate()
                                .scaleX(1.0f)
                                .scaleY(1.0f)
                                .alpha(1.0f)
                                .setDuration(100)
                                .start();
                            break;
                    }
                    return false;
                }
            });

        // Add views to layout
        dialogLayout.addView(editText);
        dialogLayout.addView(pasteButton);
        alertName.setView(dialogLayout);

        // Dialog buttons - Tech style
        alertName.setPositiveButton("OK", new DialogInterface.OnClickListener() {
                public void onClick(DialogInterface dialog, int whichButton) {
                    // Clean KEY before using
                    final String rawStr = editText.getText().toString();
                    final String str = cleanupKey(rawStr);

                    // Smooth transition to loading state
                    inputButton.animate()
                        .alpha(0f)
                        .setDuration(150)
                        .withEndAction(new Runnable() {
                            @Override
                            public void run() {
                                inputButton.setVisibility(View.GONE);
                                subtitleText.animate()
                                    .alpha(0f)
                                    .setDuration(100)
                                    .withEndAction(new Runnable() {
                                        @Override
                                        public void run() {
                                            subtitleText.setVisibility(View.GONE);

                                            // Fade in animation for loading container
                                            loadingContainer.setAlpha(0f);
                                            loadingContainer.setVisibility(View.VISIBLE);
                                            loadingContainer.animate()
                                                .alpha(1f)
                                                .setDuration(200)
                                                .start();
                                        }
                                    })
                                    .start();
                            }
                        })
                        .start();

                    new Handler().postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                try {
                                    if(Login(getContext, str)) {
                               // Animation when login successful
                                        loadingContainer.animate()
                                            .alpha(0f)
                                            .setDuration(200)
                                            .withEndAction(new Runnable() {
                                                @Override
                                                public void run() {
                                                    // Show category tab again after successful login
                                                    mCategoriesLayout.setVisibility(View.VISIBLE);
                                                    categoryScrollView.setVisibility(View.VISIBLE);
                                                    categoryTitle.setVisibility(View.VISIBLE);

                                                    // Restore original layout
                                                    LinearLayout.LayoutParams categoryParams = new LinearLayout.LayoutParams(0, WRAP_CONTENT, CATEGORY_WEIGHT);
                                                    categoryParams.setMargins(10, 0, 5, 0);
                                                    ((View) categoryScrollView.getParent()).setLayoutParams(categoryParams);

                                                    LinearLayout.LayoutParams featuresParams = new LinearLayout.LayoutParams(0, WRAP_CONTENT, FEATURES_WEIGHT);
                                                    featuresParams.setMargins(5, 0, 10, 0);
                                                    ((View) featuresScrollView.getParent()).setLayoutParams(featuresParams);
                                                }
                                            })
                                            .start();
                                    } else {
                                        // Animation when login failed
                                        loadingContainer.animate()
                                            .alpha(0f)
                                            .setDuration(200)
                                            .withEndAction(new Runnable() {
                                                @Override
                                                public void run() {
                                                    loadingContainer.setVisibility(View.GONE);

                                                    // Show button and subtitle again
                                                    inputButton.setVisibility(View.VISIBLE);
                                                    inputButton.setAlpha(0f);
                                                    inputButton.animate()
                                                        .alpha(1f)
                                                        .setDuration(200)
                                                        .start();

                                                    subtitleText.setVisibility(View.VISIBLE);
                                                    subtitleText.setAlpha(0f);
                                                    subtitleText.animate()
                                                        .alpha(1f)
                                                        .setDuration(200)
                                                        .start();

                                                    Toast.makeText(getContext, "Login failed, please check your login information!", Toast.LENGTH_SHORT).show();
                                                }
                                            })
                                            .start();
                                    }
                                } catch (Exception e) {
                                    // Animation when error occurs
                                    loadingContainer.animate()
                                        .alpha(0f)
                                        .setDuration(200)
                                        .withEndAction(new Runnable() {
                                            @Override
                                            public void run() {
                                                loadingContainer.setVisibility(View.GONE);

                                                // Show button and subtitle again
                                                inputButton.setVisibility(View.VISIBLE);
                                                inputButton.setAlpha(0f);
                                                inputButton.animate()
                                                    .alpha(1f)
                                                    .setDuration(200)
                                                    .start();

                                                subtitleText.setVisibility(View.VISIBLE);
                                                subtitleText.setAlpha(0f);
                                                subtitleText.animate()
                                                    .alpha(1f)
                                                    .setDuration(200)
                                                    .start();

                                                Toast.makeText(getContext, "Error occurred. Please try again.", Toast.LENGTH_SHORT).show();
                                            }
                                        })
                                        .start();
                                }
                            }
                        }, 1500);
                }
            });

        alertName.setNegativeButton("Cancel", new DialogInterface.OnClickListener() {
                public void onClick(DialogInterface dialog, int whichButton) {
                    // No need to show keyboard with paste button
                }
            });

        if (overlayRequired) {
            final AlertDialog dialog = alertName.create();

            // Add smooth animation when showing dialog
            dialog.getWindow().getAttributes().windowAnimations = android.R.style.Animation_Dialog;
            dialog.getWindow().setType(Build.VERSION.SDK_INT >= 26 ? 2038 : 2002);
            dialog.show();

            Button positiveButton = dialog.getButton(DialogInterface.BUTTON_POSITIVE);
            Button negativeButton = dialog.getButton(DialogInterface.BUTTON_NEGATIVE);

            positiveButton.setTextColor(TEXT_COLOR);  // Cyan
            negativeButton.setTextColor(Color.parseColor("#8E8E93"));  // Grey

            // Add effect when pressing buttons
            positiveButton.setOnTouchListener(new View.OnTouchListener() {
                    @Override
                    public boolean onTouch(View v, MotionEvent event) {
                        switch (event.getAction()) {
                            case MotionEvent.ACTION_DOWN:
                                v.setAlpha(0.7f);
                                break;
                            case MotionEvent.ACTION_UP:
                            case MotionEvent.ACTION_CANCEL:
                                v.setAlpha(1f);
                                break;
                        }
                        return false;
                    }
                });

            negativeButton.setOnTouchListener(new View.OnTouchListener() {
                    @Override
                    public boolean onTouch(View v, MotionEvent event) {
                        switch (event.getAction()) {
                            case MotionEvent.ACTION_DOWN:
                                v.setAlpha(0.7f);
                                break;
                            case MotionEvent.ACTION_UP:
                            case MotionEvent.ACTION_CANCEL:
                                v.setAlpha(1f);
                                break;
                        }
                        return false;
                    }
                });
        } else {
            alertName.show();
        }
    }
    
    private void Switch(LinearLayout linLayout, final int featNum, final String featName, boolean swiOn) {
        // Tech style container with padding and dark background
        LinearLayout switchLayout = new LinearLayout(getContext);
        switchLayout.setOrientation(LinearLayout.HORIZONTAL);
        switchLayout.setGravity(Gravity.CENTER_VERTICAL);
        switchLayout.setPadding(15, 12, 15, 12);
        switchLayout.setBackgroundColor(MENU_FEATURE_BG_COLOR);

        final Switch switchR = new Switch(getContext);

        // Tech style colors for switch
        ColorStateList buttonStates = new ColorStateList(
            new int[][]{
                new int[]{-android.R.attr.state_enabled},
                new int[]{android.R.attr.state_checked},
                new int[]{}
            },
            new int[]{
                Color.LTGRAY,
                ToggleON, // ON - Green
                Color.parseColor("#424242") // OFF - Dark Grey
            }
        );

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            try {
                switchR.getThumbDrawable().setTintList(ColorStateList.valueOf(Color.WHITE)); // White thumb
                switchR.getTrackDrawable().setTintList(buttonStates);
            } catch (NullPointerException ex) {
                Log.d(TAG, String.valueOf(ex));
            }
        }

        // Create text label with Tech style
        TextView textView = new TextView(getContext);
        textView.setText(featName);
        textView.setTextColor(TEXT_COLOR_2); // White text
        textView.setTextSize(15.0f); // Standard font size
        textView.setPadding(0, 0, 15, 0); // Space between text and switch
        LinearLayout.LayoutParams textParams = new LinearLayout.LayoutParams(
            0, WRAP_CONTENT, 1.0f);
        textView.setLayoutParams(textParams);

        // Setup switch without text, Tech style
        switchR.setText("");
        switchR.setChecked(Preferences.loadPrefBool(featName, featNum, swiOn));
        switchR.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                public void onCheckedChanged(final CompoundButton compoundButton, final boolean bool) {
                    // Effect when changing state
                    compoundButton.setEnabled(false); // Temporarily disable to create effect
                    float scale = bool ? 1.2f : 0.8f;

                    compoundButton.animate()
                        .scaleX(scale)
                        .scaleY(scale)
                        .setDuration(100)
                        .withEndAction(new Runnable() {
                            @Override
                            public void run() {
                                compoundButton.animate()
                                    .scaleX(1.0f)
                                    .scaleY(1.0f)
                                    .setDuration(100)
                                    .setInterpolator(new OvershootInterpolator(2.0f))
                                    .withEndAction(new Runnable() {
                                        @Override
                                        public void run() {
                                            compoundButton.setEnabled(true);
                                        }
                                    })
                                    .start();

                                // Save value
                                Preferences.changeFeatureBool(featName, featNum, bool);

                                // Special cases
                                if (featNum == -1) { //Save preferences
                                    Preferences.with(switchR.getContext()).writeBoolean(-1, bool);
                                    if (!bool)
                                        Preferences.with(switchR.getContext()).clear(); //Clear preferences if switched off
                                }
                            }
                        })
                        .start();
                }
            });

        // Add divider at bottom (Tech style)
        View divider = new View(getContext);
        LinearLayout.LayoutParams dividerParams = new LinearLayout.LayoutParams(MATCH_PARENT, 1);
        divider.setLayoutParams(dividerParams);
        divider.setBackgroundColor(Color.parseColor("#424242")); // Dark divider

        // Add views to layout
        switchLayout.addView(textView);
        switchLayout.addView(switchR);

        // Add components to the parent layout
        LinearLayout containerLayout = new LinearLayout(getContext);
        containerLayout.setOrientation(LinearLayout.VERTICAL);
        containerLayout.addView(switchLayout);
        containerLayout.addView(divider);

        linLayout.addView(containerLayout);
    }

    private void SeekBar(LinearLayout linLayout, final int featNum, final String featName, final int min, int max) {
        int loadedProg = Preferences.loadPrefInt(featName, featNum);

        // Tech style container with padding and dark background
        LinearLayout containerLayout = new LinearLayout(getContext);
        containerLayout.setOrientation(LinearLayout.VERTICAL);
        containerLayout.setBackgroundColor(MENU_FEATURE_BG_COLOR);
        containerLayout.setPadding(15, 10, 15, 10);

        // Reduce container margin
        LinearLayout.LayoutParams containerParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT, 
            LinearLayout.LayoutParams.WRAP_CONTENT
        );
        containerParams.setMargins(dp(10), 0, dp(10), 0);
        containerLayout.setLayoutParams(containerParams);

        // Tech style title with value
        final TextView textView = new TextView(getContext);
        textView.setText(featName + ": " + ((loadedProg == 0) ? min : loadedProg));
        textView.setTextColor(TEXT_COLOR_2);
        textView.setTextSize(15.0f);
        textView.setPadding(0, 5, 0, 10);

        // Tech style seekbar
        SeekBar seekBar = new SeekBar(getContext);
        seekBar.setPadding(dp(5), 0, dp(5), 0); // Reduce padding

        seekBar.setMax(max);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
            seekBar.setMin(min);
        seekBar.setProgress((loadedProg == 0) ? min : loadedProg);

        // Seekbar colors
        seekBar.getProgressDrawable().setColorFilter(SeekBarProgressColor, PorterDuff.Mode.SRC_ATOP);

        seekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
                public void onStartTrackingTouch(SeekBar seekBar) { 
                    seekBar.animate()
                        .scaleY(1.05f)
                        .setDuration(100)
                        .start();
                }

                public void onStopTrackingTouch(SeekBar seekBar) { 
                    seekBar.animate()
                        .scaleY(1.0f)
                        .setDuration(100)
                        .start();
                }

                public void onProgressChanged(SeekBar seekBar, int i, boolean z) {
                    int progress = i < min ? min : i;
                    seekBar.setProgress(progress);
                    Preferences.changeFeatureInt(featName, featNum, progress);
                    textView.setText(featName + ": " + progress);

                    textView.animate()
                        .alpha(0.7f)
                        .setDuration(50)
                        .withEndAction(new Runnable() {
                            @Override
                            public void run() {
                                textView.animate()
                                    .alpha(1.0f)
                                    .setDuration(100)
                                    .start();
                            }
                        })
                        .start();
                }
            });

        // Add divider (Tech style)
        View divider = new View(getContext);
        LinearLayout.LayoutParams dividerParams = new LinearLayout.LayoutParams(MATCH_PARENT, 1);
        divider.setLayoutParams(dividerParams);
        divider.setBackgroundColor(Color.parseColor("#424242")); 

        containerLayout.addView(textView);
        containerLayout.addView(seekBar);

        LinearLayout wrapperLayout = new LinearLayout(getContext);
        wrapperLayout.setOrientation(LinearLayout.VERTICAL);
        wrapperLayout.addView(containerLayout);
        wrapperLayout.addView(divider);

        linLayout.addView(wrapperLayout);
    }
    
    private void Button(LinearLayout linLayout, final int featNum, final String featName) {
        // Tech style button
        final Button button = new Button(getContext);
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(MATCH_PARENT, WRAP_CONTENT);
        layoutParams.setMargins(15, 10, 15, 10);
        button.setLayoutParams(layoutParams);
        button.setTextColor(Color.WHITE);
        button.setAllCaps(false); // Not all caps
        button.setText(Html.fromHtml(featName));

        // Tech gradient button with rounded corners
        GradientDrawable buttonBg = new GradientDrawable(GradientDrawable.Orientation.LEFT_RIGHT, 
            new int[] {Color.parseColor("#4776E6"), Color.parseColor("#8E54E9")});
        buttonBg.setCornerRadius(10); // Tech rounded corners
        button.setBackground(buttonBg);

        // Click animation
        button.setOnTouchListener(new View.OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    switch (event.getAction()) {
                        case MotionEvent.ACTION_DOWN:
                            v.animate()
                                .scaleX(0.95f)
                                .scaleY(0.95f)
                                .alpha(0.9f)
                                .setDuration(100)
                                .start();
                            break;
                        case MotionEvent.ACTION_UP:
                        case MotionEvent.ACTION_CANCEL:
                            v.animate()
                                .scaleX(1.0f)
                                .scaleY(1.0f)
                                .alpha(1.0f)
                                .setDuration(100)
                                .start();
                            break;
                    }
                    return false;
                }
            });

        button.setOnClickListener(new View.OnClickListener() {
                public void onClick(View v) {
                    // Set disabled state to prevent multiple clicks
                    button.setEnabled(false);

                    // Click effect
                    button.animate()
                        .scaleX(0.9f)
                        .scaleY(0.9f)
                        .setDuration(50)
                        .withEndAction(new Runnable() {
                            @Override
                            public void run() {
                                button.animate()
                                    .scaleX(1.05f)
                                    .scaleY(1.05f)
                                    .setDuration(100)
                                    .setInterpolator(new DecelerateInterpolator())
                                    .withEndAction(new Runnable() {
                                        @Override
                                        public void run() {
                                            button.animate()
                                                .scaleX(1.0f)
                                                .scaleY(1.0f)
                                                .setDuration(100)
                                                .withEndAction(new Runnable() {
                                                    @Override
                                                    public void run() {
                                                        // Re-enable button after animation completes
                                                        button.setEnabled(true);

                                                        // Execute feature
                                                        if (featNum == -100) {
                                                            stopChecking = true;
                                                        }
                                                        Preferences.changeFeatureInt(featName, featNum, 0);
                                                    }
                                                })
                                                .start();
                                        }
                                    })
                                    .start();
                            }
                        })
                        .start();
                }
            });

        linLayout.addView(button);
    }

    private void ButtonLink(LinearLayout linLayout, final String featName, final String url) {
        // Tech style button for links
        final Button button = new Button(getContext);
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(MATCH_PARENT, WRAP_CONTENT);
        layoutParams.setMargins(15, 10, 15, 10);
        button.setLayoutParams(layoutParams);
        button.setAllCaps(false); // Not all caps
        button.setTextColor(Color.WHITE);
        button.setText(Html.fromHtml(featName));

        // Tech gradient button with rounded corners
        GradientDrawable buttonBg = new GradientDrawable(GradientDrawable.Orientation.LEFT_RIGHT, 
            new int[] {Color.parseColor("#4776E6"), Color.parseColor("#8E54E9")});
        buttonBg.setCornerRadius(10); // Tech rounded corners
        button.setBackground(buttonBg);

        // Click animation
        button.setOnTouchListener(new View.OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    switch (event.getAction()) {
                        case MotionEvent.ACTION_DOWN:
                            v.animate()
                                .scaleX(0.95f)
                                .scaleY(0.95f)
                                .alpha(0.9f)
                                .setDuration(100)
                                .start();
                            break;
                        case MotionEvent.ACTION_UP:
                        case MotionEvent.ACTION_CANCEL:
                            v.animate()
                                .scaleX(1.0f)
                                .scaleY(1.0f)
                                .alpha(1.0f)
                                .setDuration(100)
                                .start();
                            break;
                    }
                    return false;
                }
            });

        button.setOnClickListener(new View.OnClickListener() {
                public void onClick(View v) {
                    // Click effect
                    button.animate()
                        .scaleX(0.9f)
                        .scaleY(0.9f)
                        .setDuration(50)
                        .withEndAction(new Runnable() {
                            @Override
                            public void run() {
                                button.animate()
                                    .scaleX(1.05f)
                                    .scaleY(1.05f)
                                    .setDuration(100)
                                    .setInterpolator(new DecelerateInterpolator())
                                    .withEndAction(new Runnable() {
                                        @Override
                                        public void run() {
                                            button.animate()
                                                .scaleX(1.0f)
                                                .scaleY(1.0f)
                                                .setDuration(100)
                                                .withEndAction(new Runnable() {
                                                    @Override
                                                    public void run() {
                                                        // Open URL
                                                        Intent intent = new Intent(Intent.ACTION_VIEW);
                                                        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                                                        intent.setData(Uri.parse(url));
                                                        getContext.startActivity(intent);
                                                    }
                                                })
                                                .start();
                                        }
                                    })
                                    .start();
                            }
                        })
                        .start();
                }
            });

        linLayout.addView(button);
    }

    private void ButtonOnOff(LinearLayout linLayout, final int featNum, String featName, boolean switchedOn) {
        final Button button = new Button(getContext);
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(MATCH_PARENT, WRAP_CONTENT);
        layoutParams.setMargins(15, 10, 15, 10);
        button.setLayoutParams(layoutParams);
        button.setTextColor(Color.WHITE);
        button.setAllCaps(false); // Not all caps

        final String finalfeatName = featName.replace("OnOff_", "");
        boolean isOn = Preferences.loadPrefBool(featName, featNum, switchedOn);

        // Create background for ON state
        final GradientDrawable btnOnBg = new GradientDrawable(GradientDrawable.Orientation.LEFT_RIGHT, 
            new int[] {Color.parseColor("#11998e"), Color.parseColor("#38ef7d")});
        btnOnBg.setCornerRadius(10); // Tech rounded corners

        // Create background for OFF state
        final GradientDrawable btnOffBg = new GradientDrawable(GradientDrawable.Orientation.LEFT_RIGHT, 
            new int[] {Color.parseColor("#f85032"), Color.parseColor("#e73827")});
        btnOffBg.setCornerRadius(10); // Tech rounded corners

        if (isOn) {
            button.setText(Html.fromHtml(finalfeatName + ": ON"));
            button.setBackground(btnOnBg);
            isOn = false;
        } else {
            button.setText(Html.fromHtml(finalfeatName + ": OFF"));
            button.setBackground(btnOffBg);
            isOn = true;
        }

        // Click animation
        button.setOnTouchListener(new View.OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    switch (event.getAction()) {
                        case MotionEvent.ACTION_DOWN:
                            v.animate()
                                .scaleX(0.95f)
                                .scaleY(0.95f)
                                .alpha(0.9f)
                                .setDuration(100)
                                .start();
                            break;
                        case MotionEvent.ACTION_UP:
                        case MotionEvent.ACTION_CANCEL:
                            v.animate()
                                .scaleX(1.0f)
                                .scaleY(1.0f)
                                .alpha(1.0f)
                                .setDuration(100)
                                .start();
                            break;
                    }
                    return false;
                }
            });

        final boolean finalIsOn = isOn;
        button.setOnClickListener(new View.OnClickListener() {
                boolean isOn = finalIsOn;

                public void onClick(View v) {
                    Preferences.changeFeatureBool(finalfeatName, featNum, isOn);

                    // Add animation when changing state
                    if (isOn) {
                        button.setText(Html.fromHtml(finalfeatName + ": ON"));
                        ITAnimations.toggleButtonAnimation(button, true, btnOnBg, btnOffBg);
                        isOn = false;
                    } else {
                        button.setText(Html.fromHtml(finalfeatName + ": OFF"));
                        ITAnimations.toggleButtonAnimation(button, false, btnOnBg, btnOffBg);
                        isOn = true;
                    }
                }
            });

        linLayout.addView(button);
    }

    private void Spinner(LinearLayout linLayout, final int featNum, final String featName, final String list) {
        final List<String> lists = new LinkedList<>(Arrays.asList(list.split(",")));

        // Tech style container
        LinearLayout linearLayout2 = new LinearLayout(getContext);
        LinearLayout.LayoutParams layoutParams2 = new LinearLayout.LayoutParams(MATCH_PARENT, WRAP_CONTENT);
        layoutParams2.setMargins(15, 10, 15, 10);
        linearLayout2.setOrientation(LinearLayout.VERTICAL);
        linearLayout2.setBackgroundColor(MENU_FEATURE_BG_COLOR);
        linearLayout2.setLayoutParams(layoutParams2);

        // Tech style spinner
        final Spinner spinner = new Spinner(getContext, Spinner.MODE_DROPDOWN);

        // Tech style dropdown background
        GradientDrawable spinnerBg = new GradientDrawable();
        spinnerBg.setColor(Color.parseColor("#20283C")); // Dark tech background
        spinnerBg.setCornerRadius(8); // Tech rounded corners
        spinnerBg.setStroke(1, Color.parseColor("#20BDFF")); // Cyan border
        spinner.setBackground(spinnerBg);

        spinner.setPadding(10, 10, 10, 10);
        spinner.getBackground().setColorFilter(TEXT_COLOR, PorterDuff.Mode.SRC_ATOP); // Cyan
        
        // Custom adapter with Tech styling
        ArrayAdapter<String> aa = new ArrayAdapter<String>(getContext, android.R.layout.simple_spinner_dropdown_item, lists) {
            @Override
            public View getView(int position, View convertView, ViewGroup parent) {
                View view = super.getView(position, convertView, parent);
                TextView textView = (TextView) view;
                textView.setTextColor(TEXT_COLOR_2); // White text
                textView.setTextSize(15); // Font size
                return view;
            }

            @Override
            public View getDropDownView(int position, View convertView, ViewGroup parent) {
                View view = super.getDropDownView(position, convertView, parent);
                TextView textView = (TextView) view;
                textView.setTextColor(TEXT_COLOR_2); // White text
                textView.setTextSize(15); // Font size
                textView.setPadding(15, 15, 15, 15); // Padding
                return view;
            }
        };

        aa.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        spinner.setAdapter(aa);
        spinner.setSelection(Preferences.loadPrefInt(featName, featNum));
        spinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
                @Override
                public void onItemSelected(AdapterView<?> parentView, View selectedItemView, int position, long id) {
                    Preferences.changeFeatureInt(spinner.getSelectedItem().toString(), featNum, position);
                    ((TextView) parentView.getChildAt(0)).setTextColor(TEXT_COLOR_2);

                    // Effect when selecting item
                    selectedItemView.startAnimation(new ScaleAnimation(0.7f, 1.0f, 0.7f, 1.0f,
                                                                       Animation.RELATIVE_TO_SELF, 0.5f, Animation.RELATIVE_TO_SELF, 0.5f) {{
                                setDuration(200);
                                setInterpolator(new OvershootInterpolator(1.5f));
                            }});
                }

                @Override
                public void onNothingSelected(AdapterView<?> parent) { }
            });

        linearLayout2.addView(spinner);
        linLayout.addView(linearLayout2);
    }

    private void InputNum(LinearLayout linLayout, final int featNum, final String featName, final int maxValue) {
        // Tech style button
        LinearLayout linearLayout = new LinearLayout(getContext);
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(MATCH_PARENT, WRAP_CONTENT);
        layoutParams.setMargins(15, 10, 15, 10);

        final Button button = new Button(getContext);
        int num = Preferences.loadPrefInt(featName, featNum);
        button.setText(featName + ": " + ((num == 0) ? 1 : num));
        button.setAllCaps(false); // Not all caps
        button.setLayoutParams(layoutParams);

        // Tech gradient button with rounded corners
        GradientDrawable buttonBg = new GradientDrawable(GradientDrawable.Orientation.LEFT_RIGHT, 
            new int[] {Color.parseColor("#4776E6"), Color.parseColor("#8E54E9")});
        buttonBg.setCornerRadius(10); // Tech rounded corners
        button.setBackground(buttonBg);

        button.setTextColor(Color.WHITE);

        // Click animation
        button.setOnTouchListener(new View.OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    switch (event.getAction()) {
                        case MotionEvent.ACTION_DOWN:
                            v.animate()
                                .scaleX(0.95f)
                                .scaleY(0.95f)
                                .alpha(0.9f)
                                .setDuration(100)
                                .start();
                            break;
                        case MotionEvent.ACTION_UP:
                        case MotionEvent.ACTION_CANCEL:
                            v.animate()
                                .scaleX(1.0f)
                                .scaleY(1.0f)
                                .alpha(1.0f)
                                .setDuration(100)
                                .start();
                            break;
                    }
                    return false;
                }
            });

        button.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    showNumberInputDialog(featName, featNum, maxValue, button);
                }
            });

        linearLayout.addView(button);
        linLayout.addView(linearLayout);
    }

    private void showNumberInputDialog(final String featName, final int featNum, final int maxValue, final Button button) {
        // Tech style alert dialog
        AlertDialog.Builder alertName = new AlertDialog.Builder(getContext);
        final EditText editText = new EditText(getContext);

        if (maxValue != 0)
            editText.setHint("Max value: " + maxValue);

        editText.setInputType(InputType.TYPE_CLASS_NUMBER);
        editText.setKeyListener(DigitsKeyListener.getInstance("0123456789-"));
        InputFilter[] FilterArray = new InputFilter[1];
        FilterArray[0] = new InputFilter.LengthFilter(10);
        editText.setFilters(FilterArray);

        // Tech style EditText
        editText.setTextColor(TEXT_COLOR_2); // White text
        editText.setHintTextColor(Color.parseColor("#8E8E93")); // Grey placeholder
        editText.setPadding(dp(20), dp(15), dp(20), dp(15));

        // Style EditText background - Tech style
        GradientDrawable editTextBG = new GradientDrawable();
        editTextBG.setColor(Color.parseColor("#20283C")); // Dark tech background
        editTextBG.setCornerRadius(8); // Corner radius
        editTextBG.setStroke(1, Color.parseColor("#20BDFF")); // Cyan border
        editText.setBackground(editTextBG);

        editText.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View v, boolean hasFocus) {
                    InputMethodManager imm = (InputMethodManager) getContext.getSystemService(getContext.INPUT_METHOD_SERVICE);
                    if (hasFocus) {
                        imm.toggleSoftInput(InputMethodManager.SHOW_FORCED, InputMethodManager.HIDE_IMPLICIT_ONLY);
                    } else {
                        imm.toggleSoftInput(InputMethodManager.HIDE_IMPLICIT_ONLY, 0);
                    }
                }
            });
        editText.requestFocus();

        alertName.setTitle("Enter Number");

        LinearLayout layoutName = new LinearLayout(getContext);
        layoutName.setOrientation(LinearLayout.VERTICAL);
        layoutName.setPadding(dp(20), dp(10), dp(20), dp(0));
        layoutName.addView(editText);
        alertName.setView(layoutName);

        alertName.setPositiveButton("OK", new DialogInterface.OnClickListener() {
                public void onClick(DialogInterface dialog, int whichButton) {
                    int num;
                    try {
                        num = Integer.parseInt(TextUtils.isEmpty(editText.getText().toString()) ? "0" : editText.getText().toString());
                        if (maxValue != 0 && num >= maxValue)
                            num = maxValue;
                    } catch (NumberFormatException ex) {
                        if (maxValue != 0)
                            num = maxValue;
                        else
                            num = 2147483640;
                    }

                    // Effect when value changes
                    final int finalNum = num;
                    button.setText(featName + ": " + finalNum);

                    // Animation when value changes
                    button.animate()
                        .scaleX(1.1f)
                        .scaleY(1.1f)
                        .setDuration(150)
                        .setInterpolator(new DecelerateInterpolator())
                        .withEndAction(new Runnable() {
                            @Override
                            public void run() {
                                button.animate()
                                    .scaleX(1.0f)
                                    .scaleY(1.0f)
                                    .setDuration(150)
                                    .setInterpolator(new OvershootInterpolator(1.2f))
                                    .start();
                            }
                        })
                        .start();

                    Preferences.changeFeatureInt(featName, featNum, num);
                    editText.setFocusable(false);
                }
            });

        alertName.setNegativeButton("Cancel", new DialogInterface.OnClickListener() {
                public void onClick(DialogInterface dialog, int whichButton) {
                    InputMethodManager imm = (InputMethodManager) getContext.getSystemService(getContext.INPUT_METHOD_SERVICE);
                    imm.toggleSoftInput(InputMethodManager.HIDE_IMPLICIT_ONLY, 0);
                }
            });

        if (overlayRequired) {
            AlertDialog dialog = alertName.create();
            dialog.getWindow().setType(Build.VERSION.SDK_INT >= 26 ? 2038 : 2002);
            dialog.show();

            // Tech style button colors
            Button positiveButton = dialog.getButton(DialogInterface.BUTTON_POSITIVE);
            Button negativeButton = dialog.getButton(DialogInterface.BUTTON_NEGATIVE);
            positiveButton.setTextColor(TEXT_COLOR); // Cyan
            negativeButton.setTextColor(Color.parseColor("#8E8E93")); // Grey
        } else {
            alertName.show();
        }
    }

    private void InputText(LinearLayout linLayout, final int featNum, final String featName) {
        // Tech style button
        LinearLayout linearLayout = new LinearLayout(getContext);
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(MATCH_PARENT, WRAP_CONTENT);
        layoutParams.setMargins(15, 10, 15, 10);

        final Button button = new Button(getContext);
        String string = Preferences.loadPrefString(featName, featNum);
        button.setText(featName + ": " + string);
        button.setAllCaps(false); // Not all caps
        button.setLayoutParams(layoutParams);

        // Tech gradient button with rounded corners
        GradientDrawable buttonBg = new GradientDrawable(GradientDrawable.Orientation.LEFT_RIGHT, 
            new int[] {Color.parseColor("#4776E6"), Color.parseColor("#8E54E9")});
        buttonBg.setCornerRadius(10); // Tech rounded corners
        button.setBackground(buttonBg);

        button.setTextColor(Color.WHITE);

        // Click animation
        button.setOnTouchListener(new View.OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    switch (event.getAction()) {
                        case MotionEvent.ACTION_DOWN:
                            v.animate()
                                .scaleX(0.95f)
                                .scaleY(0.95f)
                                .alpha(0.9f)
                                .setDuration(100)
                                .start();
                            break;
                        case MotionEvent.ACTION_UP:
                        case MotionEvent.ACTION_CANCEL:
                            v.animate()
                                .scaleX(1.0f)
                                .scaleY(1.0f)
                                .alpha(1.0f)
                                .setDuration(100)
                                .start();
                            break;
                    }
                    return false;
                }
            });

        button.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    showTextInputDialog(featName, featNum, button);
                }
            });

        linearLayout.addView(button);
        linLayout.addView(linearLayout);
    }

    private void showTextInputDialog(final String featName, final int featNum, final Button button) {
        // Tech style alert dialog
        AlertDialog.Builder alertName = new AlertDialog.Builder(getContext);
        final EditText editText = new EditText(getContext);

        // Tech style EditText
        editText.setTextColor(TEXT_COLOR_2); // White text
        editText.setHintTextColor(Color.parseColor("#8E8E93")); // Grey placeholder
        editText.setPadding(dp(20), dp(15), dp(20), dp(15));

        // Style EditText background - Tech style
        GradientDrawable editTextBG = new GradientDrawable();
        editTextBG.setColor(Color.parseColor("#20283C")); // Dark tech background
        editTextBG.setCornerRadius(8); // Corner radius
        editTextBG.setStroke(1, Color.parseColor("#20BDFF")); // Cyan border
        editText.setBackground(editTextBG);

        editText.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                @Override
                public void onFocusChange(View v, boolean hasFocus) {
                    InputMethodManager imm = (InputMethodManager) getContext.getSystemService(getContext.INPUT_METHOD_SERVICE);
                    if (hasFocus) {
                        imm.toggleSoftInput(InputMethodManager.SHOW_FORCED, InputMethodManager.HIDE_IMPLICIT_ONLY);
                    } else {
                        imm.toggleSoftInput(InputMethodManager.HIDE_IMPLICIT_ONLY, 0);
                    }
                }
            });
        editText.requestFocus();

        alertName.setTitle("Enter text");

        LinearLayout layoutName = new LinearLayout(getContext);
        layoutName.setOrientation(LinearLayout.VERTICAL);
        layoutName.setPadding(dp(20), dp(10), dp(20), dp(0));
        layoutName.addView(editText);
        alertName.setView(layoutName);

        alertName.setPositiveButton("OK", new DialogInterface.OnClickListener() {
                public void onClick(DialogInterface dialog, int whichButton) {
                    String str = editText.getText().toString();
                    button.setText(featName + ": " + str);

                    // Animation when text changes
                    button.animate()
                        .scaleX(1.1f)
                        .scaleY(1.1f)
                        .setDuration(150)
                        .setInterpolator(new DecelerateInterpolator())
                        .withEndAction(new Runnable() {
                            @Override
                            public void run() {
                                button.animate()
                                    .scaleX(1.0f)
                                    .scaleY(1.0f)
                                    .setDuration(150)
                                    .setInterpolator(new OvershootInterpolator(1.2f))
                                    .start();
                            }
                        })
                        .start();

                    Preferences.changeFeatureString(featName, featNum, str);
                    editText.setFocusable(false);
                }
            });

        alertName.setNegativeButton("Cancel", new DialogInterface.OnClickListener() {
                public void onClick(DialogInterface dialog, int whichButton) {
                    InputMethodManager imm = (InputMethodManager) getContext.getSystemService(getContext.INPUT_METHOD_SERVICE);
                    imm.toggleSoftInput(InputMethodManager.HIDE_IMPLICIT_ONLY, 0);
                }
            });

        if (overlayRequired) {
            AlertDialog dialog = alertName.create();
            dialog.getWindow().setType(Build.VERSION.SDK_INT >= 26 ? 2038 : 2002);
            dialog.show();

            // Tech style button colors
            Button positiveButton = dialog.getButton(DialogInterface.BUTTON_POSITIVE);
            Button negativeButton = dialog.getButton(DialogInterface.BUTTON_NEGATIVE);
            positiveButton.setTextColor(TEXT_COLOR); // Cyan
            negativeButton.setTextColor(Color.parseColor("#8E8E93")); // Grey
        } else {
            alertName.show();
        }
    }

    private void CheckBox(LinearLayout linLayout, final int featNum, final String featName, boolean switchedOn) {
        // Tech style container
        LinearLayout checkboxContainer = new LinearLayout(getContext);
        checkboxContainer.setOrientation(LinearLayout.HORIZONTAL);
        checkboxContainer.setGravity(Gravity.CENTER_VERTICAL);
        checkboxContainer.setPadding(15, 12, 15, 12);
        checkboxContainer.setBackgroundColor(MENU_FEATURE_BG_COLOR); // Dark tech background

        final CheckBox checkBox = new CheckBox(getContext);
        checkBox.setText("");

        // Tech style label
        TextView textView = new TextView(getContext);
        textView.setText(featName);
        textView.setTextColor(TEXT_COLOR_2); // White text
        textView.setTextSize(15.0f);
        textView.setPadding(0, 0, 15, 0);
        LinearLayout.LayoutParams textParams = new LinearLayout.LayoutParams(
            0, WRAP_CONTENT, 1.0f);
        textView.setLayoutParams(textParams);

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP)
            checkBox.setButtonTintList(ColorStateList.valueOf(TEXT_COLOR)); // Cyan

        checkBox.setChecked(Preferences.loadPrefBool(featName, featNum, switchedOn));
        checkBox.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
                @Override
                public void onCheckedChanged(final CompoundButton buttonView, final boolean isChecked) {
                    // Animation when state changes
                    buttonView.setEnabled(false); // Temporarily lock to prevent double click

                    buttonView.animate()
                        .scaleX(1.2f)
                        .scaleY(1.2f)
                        .setDuration(100)
                        .withEndAction(new Runnable() {
                            @Override
                            public void run() {
                                buttonView.animate()
                                    .scaleX(1.0f)
                                    .scaleY(1.0f)
                                    .setDuration(100)
                                    .setInterpolator(new OvershootInterpolator(2.0f))
                                    .withEndAction(new Runnable() {
                                        @Override
                                        public void run() {
                                            buttonView.setEnabled(true);
                                            Preferences.changeFeatureBool(featName, featNum, isChecked);
                                        }
                                    })
                                    .start();
                            }
                        })
                        .start();
                }
            });

        // Add divider (Tech style)
        View divider = new View(getContext);
        LinearLayout.LayoutParams dividerParams = new LinearLayout.LayoutParams(MATCH_PARENT, 1);
        divider.setLayoutParams(dividerParams);
        divider.setBackgroundColor(Color.parseColor("#424242")); // Dark divider

        // Add to container
        checkboxContainer.addView(textView);
        checkboxContainer.addView(checkBox);

        // Add components to the parent layout
        LinearLayout containerLayout = new LinearLayout(getContext);
        containerLayout.setOrientation(LinearLayout.VERTICAL);
        containerLayout.addView(checkboxContainer);
        containerLayout.addView(divider);

        linLayout.addView(containerLayout);
    }

    private void RadioButton(LinearLayout linLayout, final int featNum, final String featName, final String list) {
        final List<String> lists = new LinkedList<>(Arrays.asList(list.split(",")));

        // Tech style container
        LinearLayout radioContainer = new LinearLayout(getContext);
        radioContainer.setOrientation(LinearLayout.VERTICAL);
        radioContainer.setPadding(15, 10, 15, 10);
        radioContainer.setBackgroundColor(MENU_FEATURE_BG_COLOR); // Dark tech background

        final TextView textView = new TextView(getContext);
        textView.setText(featName + ":");
        textView.setTextColor(TEXT_COLOR_2); // White text
        textView.setTextSize(15.0f); // Standard text size
        textView.setPadding(0, 5, 0, 10);

        final RadioGroup radioGroup = new RadioGroup(getContext);
        radioGroup.setOrientation(LinearLayout.VERTICAL);
        radioGroup.addView(textView);

        for (int i = 0; i < lists.size(); i++) {
            final RadioButton radioButton = new RadioButton(getContext);
            final String finalfeatName = featName, radioName = lists.get(i);

            // Tech style radio buttons
            View.OnClickListener first_radio_listener = new View.OnClickListener() {
                public void onClick(final View v) {
                    // Animation when selecting
                    v.animate()
                        .scaleX(1.2f)
                        .scaleY(1.2f)
                        .setDuration(150)
                        .withEndAction(new Runnable() {
                            @Override
                            public void run() {
                                v.animate()
                                    .scaleX(1.0f)
                                    .scaleY(1.0f)
                                    .setDuration(150)
                                    .setInterpolator(new OvershootInterpolator(1.5f))
                                    .start();

                                textView.setText(featName + ": " + radioName);
                                Preferences.changeFeatureInt(finalfeatName, featNum, radioGroup.indexOfChild(radioButton));
                            }
                        })
                        .start();
                }
            };

            radioButton.setText(lists.get(i));
            radioButton.setTextColor(TEXT_COLOR_2); // White text
            radioButton.setTextSize(14.0f); // Font size
            radioButton.setPadding(0, 8, 0, 8); // Padding

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP)
                radioButton.setButtonTintList(ColorStateList.valueOf(TEXT_COLOR)); // Cyan

            radioButton.setOnClickListener(first_radio_listener);
            radioGroup.addView(radioButton);
        }

        int index = Preferences.loadPrefInt(featName, featNum);
        if (index > 0) {
            textView.setText(featName + ": " + lists.get(index - 1));
            ((RadioButton) radioGroup.getChildAt(index)).setChecked(true);
        }

        // Add divider (Tech style)
        View divider = new View(getContext);
        LinearLayout.LayoutParams dividerParams = new LinearLayout.LayoutParams(MATCH_PARENT, 1);
        divider.setLayoutParams(dividerParams);
        divider.setBackgroundColor(Color.parseColor("#424242")); // Dark divider

        // Container with divider
        LinearLayout containerLayout = new LinearLayout(getContext);
        containerLayout.setOrientation(LinearLayout.VERTICAL);
        containerLayout.addView(radioContainer);
        radioContainer.addView(radioGroup);
        containerLayout.addView(divider);

        linLayout.addView(containerLayout);
    }

    private void Collapse(LinearLayout linLayout, final String text, final boolean expanded) {
        LinearLayout.LayoutParams layoutParamsLL = new LinearLayout.LayoutParams(MATCH_PARENT, MATCH_PARENT);
        layoutParamsLL.setMargins(0, 5, 0, 0);

        LinearLayout collapse = new LinearLayout(getContext);
        collapse.setLayoutParams(layoutParamsLL);
        collapse.setVerticalGravity(16);
        collapse.setOrientation(LinearLayout.VERTICAL);

        final LinearLayout collapseSub = new LinearLayout(getContext);
        collapseSub.setVerticalGravity(16);
        collapseSub.setPadding(0, 5, 0, 5);
        collapseSub.setOrientation(LinearLayout.VERTICAL);
        collapseSub.setBackgroundColor(MENU_FEATURE_BG_COLOR); // Dark tech background
        collapseSub.setVisibility(View.GONE);
        mCollapse = collapseSub;

        final TextView textView = new TextView(getContext);

        // Tech style header
        GradientDrawable headerBg = new GradientDrawable();
        headerBg.setColor(CategoryBG); // Dark tech grey
        headerBg.setCornerRadius(8); // Tech rounded corners
        textView.setBackground(headerBg);

        textView.setText("▽ " + text + " ▽");
        textView.setGravity(Gravity.CENTER);
        textView.setTextColor(TEXT_COLOR); // Cyan text

        // Use Typeface.create for Medium font
        Typeface mediumTypeface = Typeface.create("sans-serif-medium", Typeface.NORMAL);
        textView.setTypeface(mediumTypeface);

        textView.setPadding(0, 15, 0, 15);

        if (expanded) {
            collapseSub.setVisibility(View.VISIBLE);
            textView.setText("△ " + text + " △");
        }

        textView.setOnClickListener(new View.OnClickListener() {
                boolean isChecked = expanded;

                @Override
                public void onClick(View v) {
                    boolean z = !isChecked;
                    isChecked = z;
                    if (z) {
                        // Animation when expanding
                        textView.animate()
                            .scaleY(0.95f)
                            .setDuration(100)
                            .withEndAction(new Runnable() {
                                @Override
                                public void run() {
                                    textView.animate()
                                        .scaleY(1.0f)
                                        .setDuration(100)
                                        .start();
                                }
                            })
                            .start();

                        collapseSub.setVisibility(View.VISIBLE);
                        collapseSub.setAlpha(0f);
                        collapseSub.animate()
                            .alpha(1f)
                            .setDuration(300)
                            .start();

                        textView.setText("△ " + text + " △");
                        return;
                    }

                    // Animation when collapsing
                    textView.animate()
                        .scaleY(0.95f)
                        .setDuration(100)
                        .withEndAction(new Runnable() {
                            @Override
                            public void run() {
                                textView.animate()
                                    .scaleY(1.0f)
                                    .setDuration(100)
                                    .start();
                            }
                        })
                        .start();

                    collapseSub.animate()
                        .alpha(0f)
                        .setDuration(200)
                        .withEndAction(new Runnable() {
                            @Override
                            public void run() {
                                collapseSub.setVisibility(View.GONE);
                            }
                        })
                        .start();

                    textView.setText("▽ " + text + " ▽");
                }
            });

        collapse.addView(textView);
        collapse.addView(collapseSub);
        linLayout.addView(collapse);
    }

    private void Category(LinearLayout linLayout, String text) {
        TextView textView = new TextView(getContext);

        // Tech style category header
        GradientDrawable headerBg = new GradientDrawable();
        headerBg.setColor(CategoryBG); // Dark tech grey
        headerBg.setCornerRadius(0); // No rounded corners for section headers
        textView.setBackground(headerBg);

        textView.setText(Html.fromHtml(text));
        textView.setGravity(Gravity.LEFT); // Left-aligned
        textView.setPadding(15, 8, 15, 8); // Padding
        textView.setTextColor(Color.parseColor("#20BDFF")); // Cyan text
        textView.setTextSize(13); // Small text

        // Use Typeface.create for Medium font
        textView.setTypeface(Typeface.create("sans-serif-medium", Typeface.BOLD)); // Bold font for title
        textView.setTextSize(12); // Reduced from 13

        textView.setAllCaps(true); // Uppercase for section headers
        linLayout.addView(textView);
    }

    private void TextView(LinearLayout linLayout, String text) {
        TextView textView = new TextView(getContext);
        textView.setText(Html.fromHtml(text));
        textView.setTextColor(TEXT_COLOR_2); // White text
        textView.setTextSize(14); // Font size
        textView.setPadding(15, 10, 15, 10); // Padding

        // Add Tech style divider
        LinearLayout container = new LinearLayout(getContext);
        container.setOrientation(LinearLayout.VERTICAL);
        container.addView(textView);

        View divider = new View(getContext);
        LinearLayout.LayoutParams dividerParams = new LinearLayout.LayoutParams(MATCH_PARENT, 1);
        divider.setLayoutParams(dividerParams);
        divider.setBackgroundColor(Color.parseColor("#424242")); // Dark divider
        container.addView(divider);

        linLayout.addView(container);
    }

    private void WebTextView(LinearLayout linLayout, String text) {
        // Container for WebView
        LinearLayout container = new LinearLayout(getContext);
        container.setOrientation(LinearLayout.VERTICAL);
        container.setBackgroundColor(MENU_FEATURE_BG_COLOR); // Dark tech background

        WebView wView = new WebView(getContext);
        wView.loadData(text, "text/html", "utf-8");
        wView.setBackgroundColor(0x00000000); // Transparent
        wView.setPadding(15, 10, 15, 10); // Padding
        wView.getSettings().setAppCacheEnabled(false);
        container.addView(wView);

        // Add Tech style divider
        View divider = new View(getContext);
        LinearLayout.LayoutParams dividerParams = new LinearLayout.LayoutParams(MATCH_PARENT, 1);
        divider.setLayoutParams(dividerParams);
        divider.setBackgroundColor(Color.parseColor("#424242")); // Dark divider
        container.addView(divider);

        linLayout.addView(container);
    }

    private boolean isViewCollapsed() {
        return rootFrame == null || mCollapsed.getVisibility() == View.VISIBLE;
    }

    private int convertDipToPixels(int i) {
        return (int) ((((float) i) * getContext.getResources().getDisplayMetrics().density) + 0.5f);
    }

    private int dp(int i) {
        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP, (float) i, getContext.getResources().getDisplayMetrics());
    }

    public void setVisibility(int view) {
        if (rootFrame != null) {
            rootFrame.setVisibility(view);
        }
    }

    public void onDestroy() {
        if (rootFrame != null) {
            mWindowManager.removeView(rootFrame);
        }
    }
    
    public boolean Login(Context ctx, String str) {
        try {
            l = Check(getContext, str);
            Log.d("Menu", "Login response: " + l);
            data = new JSONObject(l.toString());

            if(data.has("res") && data.getString("res").equals("OK")) {
                String userName = "H-MOD";

                if(data.has("user")) {
                    userName = data.getString("user");
                    Log.d("Menu", "Found username: " + userName);
                } else {
                    Log.d("Menu", "No username found in response");
                }

                if (title != null) {
                    final String finalUserName = userName;
                    title.post(new Runnable() {
                            @Override
                            public void run() {
                                title.setText(Html.fromHtml("<b><big><font color='" + NumberTxtColor + "'>" + finalUserName + "</font></big></b>"));

                                // Add light scale up effect for title when login successful
                                title.setScaleX(0.9f);
                                title.setScaleY(0.9f);
                                title.animate()
                                    .scaleX(1.0f)
                                    .scaleY(1.0f)
                                    .setDuration(300)
                                    .setInterpolator(new OvershootInterpolator(1.2f))
                                    .start();
                            }
                        });
                } else {
                    Log.e("Menu", "Title view is null");
                }

                // Save expiry info instead of displaying
                if (data.has("hd")) {
                    keyExpiryInfo = Utils.fromBase64String(data.getString("hd"));

                    // Hide subtitle from main menu
                    if (subTitle != null) {
                        subTitle.setText("");
                    }
                }

                prefs.write(USER, str);
                createCategoryList();

                // Check if there are any categories
                if (!tabToCategory.isEmpty()) {
                    currentCategory = tabToCategory.values().iterator().next();
                    loadFeaturesForCategory(currentCategory);
                } else {
                    // Display message when no categories
                    TextView emptyText = new TextView(getContext);
                    emptyText.setText("No categories from C++. Please update category list.");
                    emptyText.setTextColor(TEXT_COLOR_2);
                    emptyText.setPadding(20, 20, 20, 20);
                    emptyText.setGravity(Gravity.CENTER);
                    mFeaturesLayout.addView(emptyText);
                }

                return true;
            } else {
                Log.e("Menu", "Login failed, response: " + l);
                return false;
            }
        } catch(Exception e) {
            Log.e("Menu", "Login error: " + e.getMessage(), e);
            Toast.makeText(getContext, l.toString(), Toast.LENGTH_LONG).show();
            return false;
        }
    }

    private void featureList(String[] listFT, LinearLayout linearLayout) {
        int featNum, subFeat = 0;
        LinearLayout llBak = linearLayout;

        for (int i = 0; i < listFT.length; i++) {
            boolean switchedOn = false;
            String feature = listFT[i];

            // Skip tab prefix
            String featureWithoutTab = feature;
            if (feature.matches("tab\\d+_.*")) {
                int underscoreIndex = feature.indexOf('_');
                if (underscoreIndex > 0) {
                    featureWithoutTab = feature.substring(underscoreIndex + 1);
                }
            }

            if (featureWithoutTab.contains("_True")) {
                switchedOn = true;
                featureWithoutTab = featureWithoutTab.replaceFirst("_True", "");
            }

            linearLayout = llBak;
            if (featureWithoutTab.contains("CollapseAdd_")) {
                linearLayout = mCollapse;
                featureWithoutTab = featureWithoutTab.replaceFirst("CollapseAdd_", "");
            }

            String[] str = featureWithoutTab.split("_");

            // Get feature number
            if (TextUtils.isDigitsOnly(str[0]) || str[0].matches("-[0-9]*")) {
                featNum = Integer.parseInt(str[0]);
                featureWithoutTab = featureWithoutTab.replaceFirst(str[0] + "_", "");
                subFeat++;
            } else {
                featNum = i - subFeat;
            }

            String[] strSplit = featureWithoutTab.split("_");
            switch (strSplit[0]) {
                case "Toggle":
                    Switch(linearLayout, featNum, strSplit[1], switchedOn);
                    break;
                case "SeekBar":
                    SeekBar(linearLayout, featNum, strSplit[1], Integer.parseInt(strSplit[2]), Integer.parseInt(strSplit[3]));
                    break;
                case "Button":
                    if (featNum == 100) { // Logout
                        prefs.clear();
                        key = "";
                        mFeaturesLayout.removeAllViews();
                        InputLogin(mFeaturesLayout, 1, "Paste KEY here ...");
                    }
                    Button(linearLayout, featNum, strSplit[1]); 
                    break;
                case "ButtonOnOff":
                    ButtonOnOff(linearLayout, featNum, strSplit[1], switchedOn);
                    break;
                case "Spinner":
                    TextView(linearLayout, strSplit[1]);
                    Spinner(linearLayout, featNum, strSplit[1], strSplit[2]);
                    break;
                case "InputText":
                    InputText(linearLayout, featNum, strSplit[1]);
                    break;
                case "InputValue":
                    if (strSplit.length == 3)
                        InputNum(linearLayout, featNum, strSplit[2], Integer.parseInt(strSplit[1]));
                    if (strSplit.length == 2)
                        InputNum(linearLayout, featNum, strSplit[1], 0);
                    break;
                case "CheckBox":
                    CheckBox(linearLayout, featNum, strSplit[1], switchedOn);
                    break;
                case "RadioButton":
                    RadioButton(linearLayout, featNum, strSplit[1], strSplit[2]);
                    break;
                case "Collapse":
                    Collapse(linearLayout, strSplit[1], switchedOn);
                    subFeat++;
                    break;
                case "ButtonLink":
                    subFeat++;
                    ButtonLink(linearLayout, strSplit[1], strSplit[2]);
                    break;
                case "Category":
                    subFeat++;
                    Category(linearLayout, strSplit[1]);
                    break;
                case "RichTextView":
                    subFeat++;
                    TextView(linearLayout, strSplit[1]);
                    break;
                case "RichWebView":
                    subFeat++;
                    WebTextView(linearLayout, strSplit[1]);
                    break;
            }
        }
    }
}



// Utils class for handling base64 strings
class Utils {
    public static String fromBase64String(String encodedString) {
        try {
            byte[] decodedBytes = Base64.decode(encodedString, Base64.DEFAULT);
            return new String(decodedBytes, "UTF-8");
        } catch (Exception e) {
            Log.e("Utils", "Error decoding base64 string: " + e.getMessage());
            return "";
        }
    }
}

