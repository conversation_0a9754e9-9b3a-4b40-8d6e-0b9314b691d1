#***************************************************************************
#                                  _   _ ____  _
#  Project                     ___| | | |  _ \| |
#                             / __| | | | |_) | |
#                            | (__| |_| |  _ <| |___
#                             \___|\___/|_| \_\_____|
#
# Copyright (C) 1998 - 2012, <PERSON>, <<EMAIL>>, et al.
#
# This software is licensed as described in the file COPYING, which
# you should have received as part of this distribution. The terms
# are also available at https://curl.haxx.se/docs/copyright.html.
#
# You may opt to use, copy, modify, merge, publish, distribute and/or sell
# copies of the Software, and permit persons to whom the Software is
# furnished to do so, under the terms of the COPYING file.
#
# This software is distributed on an "AS IS" basis, WITHOUT WARRANTY OF ANY
# KIND, either express or implied.
#
###########################################################################

# This should most probably benefit from getting a "Requires:" field added
# dynamically by configure.
#
prefix=/Users/<USER>/workspaces/openssl_for_ios_and_android/tools/../output/android/curl-android-arm64-v8a
exec_prefix=${prefix}
libdir=${exec_prefix}/lib
includedir=${prefix}/include
supported_protocols="DICT FILE FTP FTPS GOPHER HTTP HTTPS IMAP IMAPS POP3 POP3S RTSP SMB SMBS SMTP SMTPS TELNET TFTP"
supported_features="SSL IPv6 UnixSockets libz AsynchDNS NTLM NTLM_WB TLS-SRP"

Name: libcurl
URL: https://curl.haxx.se/
Description: Library to transfer files with ftp, http, etc.
Version: 7.51.0
Libs: -L${libdir} -lcurl
Libs.private: -lssl -lcrypto -lssl -lcrypto -lz -lz
Cflags: -I${includedir} -DCURL_STATICLIB
