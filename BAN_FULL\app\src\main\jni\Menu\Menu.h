bool iconValid, settingsValid, initValid;

//Big letter cause crash
void setText(JNIEnv *env, jobject obj, const char* text){
    //https://stackoverflow.com/a/33627640/3763113
    //A little <PERSON><PERSON> calls here. You really really need a great knowledge if you want to play with JNI stuff
    //Html.fromHtml("");
    jclass html = (*env).FindClass(OBFUSCATE("android/text/Html"));
    jmethodID fromHtml = (*env).GetStaticMethodID(html, OBFUSCATE("fromHtml"), OBFUSCATE("(Ljava/lang/String;)Landroid/text/Spanned;"));

    //setText("");
    jclass textView = (*env).FindClass(OBFUSCATE("android/widget/TextView"));
    jmethodID setText = (*env).GetMethodID(textView, OBFUSCATE("setText"), OBFUSCATE("(Ljava/lang/CharSequence;)V"));

    //Java string
    jstring jstr = (*env).NewStringUTF(text);
    (*env).CallVoidMethod(obj, setText,  (*env).CallStaticObjectMethod(html, fromHtml, jstr));
}

jstring Icon(JNIEnv *env, jobject thiz) {
    iconValid = true;

    //Use https://www.base64encode.org/ to encode your image to base64
    return env->NewStringUTF(
            OBFUSCATE("iVBORw0KGgoAAAANSUhEUgAAAFoAAABaCAYAAAA4qEECAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAABjSSURBVHic7Z15eFTV+cc/Z+7MZN8ICQQCWSCAbGGrIAhFQRYRtFiRXVyqxWq1Lmhr21/V0rq1orVaUVQQVBAQQdlERVZZA4Swk4UsBEJ2ss1yz++Pe2dlkkwEFITv8yTPzL1n/c573vOe92yCSxcGrm0dRRtza8JlHKHCjE1pgdkuALAqKma1kDLFQqUs4JSlgE0nygD50xbbN8RPXQAdCne07Ugrw0AC1Z6YRBeMakfMRKFIBe9yCjcupQCQ2A02akUxKoewGPZTo6aRZd/IF3nHAfVHrItP/HRED2geRmrYcMLlaALkUILtcYDQSBS4BFN4fNTgHsZHFRzhVSGpMeRglWs4Y/yC/Zav2ZpXc5Fq1CB+bKIN3N1uMC3sdxGs3kagGobUy+BdEm8F4Kuk5/wAvt5J/bOQ1BlKqGIxBYb3+TB7u49cLhp+HKJ7twpmgPlOIuWjhNi7IfzMVzokVoAxCJQgUEzac9UGtmqw1wISV0vwAyoqVcYdlCiz+Pb4Ug5g+YE18xsXl+jOmLkp4T6i5NMEqW18t3bp0LNgMBMd05WBLVK5NrYLXSPb0SGiDc0Cw6m1Wzlrq8FqtyIQmI0mQo1BmAwKRTVlHCnPZV/pMbafzmBDYRpniw+BtNUj7RKEAImkSjlKieF53sj6GLBfLCouHtHTk8YQa/8nIeo1HurBo0kLCE9kQvuRjEsaQr8WXdl75hjfFe5mW1EGe0uPUVx2AupKQdp1gvT4Eo0sYYSgZsRGtKVXsxT6xnZjcMuedIxKYEPBHj7J+oplx1ZB1clz1bpLcUjOKrvJNz7Je8e/vRh0XHiix7dqQ5L5DSJsozF4pe+omDmc2675NY93nUh0YDhzj67kk+NryTm5E+QFaMUSUILpGN+Pick3MTllBMfK83k5fT7rjizXVI6vPkFip8z4IRnmJ1lx5Mz5F8SFC0m04OF2U4i1vkqAvRlSeEkfEJbIs72nc3fHW/jo2BpeTp9P8ak0PPqkC/3TS0AYaNuqHzO6T+GWhIG8sX8Rr6TNhqpCrzz1jrPOkM9J44O8lbX8QhXjwlRrZPtwutnfJNo6EaTe1elJSwmhrXn5uhn8Kmkwz+2ew7x9c8FafsGy9w+6qgqK4Xc97uHJ1Mm8c3AZM7fNgtpiz6JICVLYOWP6LxnKDFYdqzvf3M+/ptPiU0hQFhNs7+6p+yQogTz4i98zo8dU/rLzbT7c8x7Yq11hfmSeQc9TSjBH8HCfB3ms2yT+uP0NPtkzx9V5Slx9SKWyiezgccw/ePJ8sj+/qt6XNJAE2xLMagzCjWUpaBXXl5UjZrE273tmbJoJNUVadpfCWNT5I0sIiWfODc+TEt6WEasfobo4w0uVCKhVcsg1jOHdrH0/NEvlBxd2etIYEmyfYVYjnSRLCcLEIwOe5rXrHmfM2sf5dM87mhSLS4RkcCuHAEsFy48sJ626iPWj3uSMNJB+cidO9SckmNRIQhlHx+ab2Vmae15ZNgnTkycSb30fo2p2FkgCwbGsHP0u2ZUneXDdE2Cp4JKR4oYgdb0SFMunI/9Lja2WqasfAovejzhGl1ZDFZnGsbyXtbapWRiaXKjpSWNcJOujNikJiu7MwYkree/wCh5c+QBYKi8tKW4IQmh/NUXc8dl4dhYdYNfElRCeoL2X+nuzDCHZtpTftOvf1Cyapjo0nfwZRhnkUhfQpnV/dtw2lzFrHmfDkcVcFlLsC7re3pa7icM1Fawb9SZzcjZhrT3tCmCQZkLU22gf+SW7y4v8Tdp/oifHtydZriVARrqsC0nHhCF8NfK/9F42lbyTW1wFuhyJdkNOSQariw7z/Zg5LMjbTvXZk64WaiSYIMMIwpst5HBZlT/p+ac67ogJJVlZTIAa43ymS/LK4bPosmQ8ZUVpOB1AlznJjn7naO56Bn7xADtu/YDA6C4OLyAgIcieTDf1Y3pj8idJ/4hODn6LEHuqu50cGN2Z7255mz6fT6W2JIPLn10fkJBXsJVhqx8hfewCCI3HaZALIMI+mMFJM/1JqnHV8bukKbS0/VVzbTqsixjSxy1h9NrHycvfisuG/plBr1ZJeTYHaspYOPRF3jmwGFSL/lIKgtS+tI/cwq7yrIaSapjoSa3jSZTLUAh2Th8JEyvHfsSs/R+z4fBiNF/wz5RocGrDrKIMIsLacE/X8Sw/ssLdzjYQpAwiJvoDDpTWO1RvWHW0Mb2BWY12Dkel4JH+MzhReZJFabPddNaVgVc2z6RFUDN+1f1uh7cPEBBoTyRZNqhC6mfpt8m3kGRZDvp8iJS0iuvH16Pe4Jp5Q7TByJXDsQYJhMSROXkN7Rfdjlp21PXOho1DpgHMy9ruK6pvie6NiVj7Swi3SSclgC+H/5uRq/+ged6uNJIdqCrgrvV/Y/XwWYDB5axSMJKgvkA9zPgm+vrEewi1d9K+aMPP3/b5PV8X7CQ7b5Nr6ulKhICNh5dSVlfJiM4T3Z5LCLUPZnrS6HqieeG6+CCGKYcIsbV1WhkhrciesobEuTdA7QWdeLh8EZHMsXFLaf/BQLBWas+khEpjGn/P6YPXWpJzrY47YibRzDoViXBYEy8Nfo6FmevYe+Lbn7eF0RTUlhIRFk+32FR25G3GqUMCZEuSo7ewq+y4e3Bv1SEIsz2qzZLo0hyWwNikG5i3Zw5XrmL2AQHPb3uNJ1OngDkS19SdFMTYH/MO7inR96YMIs7yNEIIxw/07IA/8nnOd+zN33yVZ29YqwkLbU37Zimknfweza4WYJSJNI9aTEa5U896SnSsZZqzpxOAOZy7O45m7r55Lp/tVbhB8vyut/ljj2ng7vJQMNJGTHEP6SK6e4sQguRY9ymp2zrdzifH12oO8Ku6+VwIAZUn2FV0iB7JN7lN5ksIlhNw0xguoq8PGU6gGu78LgV/6DqBF/fNv6oyGsFL6R/yRFd3Uw8IsScwtf21jkcuoiPto5AOA1lCWALNg6IoPrX7xyrv5QkBu7O/YXCr3mDS5VTqjpA4+82OYA6iFUxymGsthuDOlJHMO7qSS3Rd96UFew3LczbQP+lG7bvuiMKsjnQE0Yi+PTGFILW1u4oYlzSUTzKbPAd5ZUIIPslcx7jkoZ5GQyDdGdI6GhxEx4sBHktpDSYGtOhKTsGOq/rZT2zI2cCoNv093RNGu4nkgOvAQXSw7O1OaFRMF/YWH9Mc3Fc1h3+oOY1VtUFoK1yzMBIi7D3BQbRCV/c4v2zRk/Und7t0TX2Q6OvUGnjvEbahkroFaCys93tf390/1Jeer7GB1AP/AAFbfzKNHi1TPR8GiC4ARsCAonZ0Ln8S8IuYLnxdsKPxlM0hJLbohSpVTuRvwVfpWsX1xayYyS5Kh7qyBpOLbXktwcZAJCo5eVvxucdHAgFhJMb2ACC/qghrySHPZVwICG7J9fH9iQ6M4HRNCVtzt0DtaTwlRxDavBvNAyOdT1SpcqI8FypzQapNGj/sKMqgb0wX9mSucqaPQe0MYOS6+EjMspl7RbpFteNfGR81mnB4ZDuyJnyEXQXjqx1APXcfzp5fzScmJJDOn97Nwex1DaQm2HLLO7SLigYJHRZO4Wjueq8w2g95b+q9vDv4cQDezVjLb1beq7+WYDDzf4Of45lekzG5jXstdvjT9tn8a/M/tEXtemtdPOQFhrft7kpen+TeeyaP+zf9g+1HV/jXTwnJntJj3Jsy2rWjAEAhgd6YjMSbW2O0ePg8OkS0oaTshB+pXwTolX2y22Tu9/YW6mudH+06sZ7VqII3R/yH6V1GUWmR/G3PB+wtPc610dfwROokXul/P1EBYfz56xnnNL4VmTtZl7+NAGMgg1r24JbE3my77U1+810K727/d+NkS8GRshw6RSR4PjcTSnDLSCNhUtt25kxJEBUUDrWlP5nFodphcofh3L8uSlM3buVoHdeXrtFxHkKjQdIx4UamdxlFrQ1aLRjF2TP7AMGXSN46uJS8yUt4ptcEXt2/kOJTuzzyXJzzDfN2vg5S8DLQM3kkO8bOZvagP7AqbzP5Bd83rEaEpKrqFPEhsZ7PFSloZmxpIJhA1ww3YAyizmbhp9wDuSLre4JMMLbTWDxFT/BEt0kArM7c6RlJCmZ0nwrAf/Yv5uyZdNyFp7BwGx8c0sYFT3WfpKXro0U4kJa5ilf2fIoQ8NK1D9O41AmorSA8MBRPX50URAaYDdgMLTzqogRz1lbzk3rr3j60DCQ81m0SrtGqBFMo93QaQ1mdnXmZq71iCYa2vlbTu9nfOB55WE5Ls9eDgKGt+1IvcW5x5hz5HIBbEgaCMDZecGnDKAygeqctWxoJ0HtWh85TTJpEN0FtKAJW374Imzy3FUQFBvqfEFoZ9pYcZfPJowyI60BQdGdqSg4AcFPKaMLNBl5M+xTV4FVAg4m4EM3XkFlZ4FNaT5zVFu23CYnVMmqk1R6t0JZCh5sNEBABdcWNFF5FVVUwKJ5pB4PxPJaie2B4Qo9zK+fdYUnJkM4T6BvT2a1o8MLmf4K9xhkn0BjAK+nzGRD3LI91ncjM7/4MCF3C4eX0+dya+EvPvISCorfYWruPnV1CYtcFQTE4pLMRabLbXMGMZjiPnSxGahSJtLgUvd1KgNHsfwoSrCqYX+viIssNRQ8eonmIIz3BtJRRTO442CP+C9tfd+1tERAeEMqyg0uovuFZHu56BzM3PgdhrRneJpVdp7MpPpWGOXmoZ0aqjTobBBkh3BTM2XPKKTAbtHLU2fxjzGAKcJYRS7U/MTAYDFpv7i7A1WDEoBZ6hLVXE2oMwmlQ+pO8AKQVsHq+kCC9mueUb57mvs3hnl1AbYmHjyBYCQBLOXOPfMn0LqPonXQTA1qmIoQmzSAJNgR45WUlq6KQzs1a0i2qHQUF27wEVtI5KgmAIxW5ftXtuubagDn/7Fl9YX0jEYRRU5/CO21RaKBGWjxItdXoEu3nZoCmmoBn86g7cxBLif5XfEAbgZ0DyYv7PgTgT6nTeLTreGptsPDAYi1bH/l+lr0egHs6jHEkgXOHmN6aAJbnbGy8nFLwVKpmxcw/ugqEH7uXA8OpqD3rRbSQlNVZDFSKk9obV+9eWlMBgVGNJ9xUOBcGuj8Tvn0qQpCTv5VDpacY264fSeFRLDj6lbYn0Cckf9v9DlYVxrUfwM1d9Sk7fd3g7an3MqxtKrVW+NfeD+oXEAkoITx34wuMTuxDpUXy9PZZfjQASUhIC/KqTuMR2C4kJbZCI3mWfHoIOyaMDjKOlOfSLLItJad+4sUyUuXV9I95e9CjALycrkl4fSTZSo8w+ZvnWTj0L3w58h9s7nE3+0qO0DO6E/1atgMJ49b9GSpz8P7Bn+lxD+OThxNsNNOreSfCzILyOpW+n0+DyhP1Z+osq6BDZAKHynM8m5uVCjYWlhrYmleGRZQ4XwhILz1Oj6iURnlQpcSman6E+n5yq7RhUzVnTWO2uVXasalgR3UmN3v/x9TZ4XDZaQ6f2OBYO4FND6tKG84N+MCitNn0WvoAu07nMCAuheldRtGvZTt2nMqi+5L7WJE+F3eSraoVmwrtIpozrE0q/Vtew8mqMzy7awGR71/P4Rw3m7whCEmPqPaklx53USEBG7mATYv+58QNhNsGOuLc1u1urolM4p8b/1p/wo4lqwZ9ml31ZXtLEHqnpVqBhrxhEoRZS1PacNqhEi0PKXHtbJXaAEIoun63ejLhKFtwLAFBUdRVl+gbSr3yl+jpeIzk9LLqtqm/fZCEN29+m9mHl7m8d1JChWkhM7PHaznY2e8e57tTaQyO69WwBAo0/adaQNb56GnRLAnVgnZiQSNDeim0CvoKKy26VeOWubTpz23OinqWTYXqQuqKD0LNKXwPuQH0PJ1/VjxcEk3AoJY92VO41403AXUyAxymRbXY5V7Q0qIMuke3B28TyhccHZyvUrk/duzlaygdX6+dHaiH38uVlq8OFvc47nHFOUHqzbSp61iCYglQjFBV4JlmuZIGDqLz5GakkE6yVStbTu0nsdW1XJ3L8g+DEgbxZa4++eHg2Wqwklm3FRxEL8k+So3Id/8hFmWt406P1TdXUS8kjE8eyqLMdZ4toU7s4+v8YnCNSuzUsc6d1YVHVzE15Wb8HrhcsZCgBDImYRBbsr7xeIwFx5yWG4sVxhXOXkAAlTmcqSklukUvGp8tvYIhBb0Sb2R9wS6wlevP9P8nTSsdwVxEb6paQ62hwi0gr+7/mBndJ+PYkXUVXtCtixndpvDK/o88OapScph3zLlxyEX0vlNV1Iil7idxLTu0hAnthoE5ouk+jSsBAghrS6+YTuzJ/MqNIwnVwuN4N08FfNr8AejWhwAs5bx/eAV3dZ9Kg+s3rlRI+EvvB3hhzwe6Pa9zZBc2cuWH7kHPceXw14TdhNpcXvzQBI5N+Jz27/UHe5WPKFcwAmPIvutbEt+/3m0SWUK5soaZJ0a4B/U2KSSVxllOqQaozGFp1rdM7XHvVT3thCa5f+n7CC/v/RAsZS7XgBSSIuXf3jHOZc6x/S3Y3tbppvbY/lZUb9QrBxIi2nltf9P1bYXi5/a3vAobPaJqCFdHAdoaU0sltUoI4zuMZu1xffb5St5qIQULR77FP/fM5VjhdhcXUkjylensKjvkHcX3aGRT9nucVQ45nSMC/rfzdYa06kNi/PUXrfyXBaRkYMexRAWEserAAlfDlgIqjBt5O9vn6Y++id6FldPKDFyHL4Pdwqg1j7FqxKtgDr8yLRApIaQVcwf/jRFrHkXTDo5JbWzkiieph5n6FxvsLD1Cn2Y9CZLannABlZX5mAJjmNZ5HF8c/eLKU9PCyPJb5/LC3nkczP7ac/66zPQWb2a9U1/Uhh0ZudaHsBiKnTa0gFlbX6JtWBzjet7vNvH5c4ZrvfQTA57hdE0Jy/a+7+Z/l1CrZJMpnmkolYaJXpCfx0nlD55efRs3L7+XZ3s/QKe2N55nJS4HCJAw5Jo7ubPdUO5b84g2qeBozqqwkW94gE8zyxtKpXHX3H+zPqREWQC4hLfmND0/m8LKkbOIiu15PrW4DCBJaXsD/7v+KX6xZKLrJAPHyxLjK8xu/GRH/3ygmdXTqVL2aoKt6ZDa4gP88osH2HnrPAKbdf6ZqRFXPeLj+rN2xOt0XzoZzuZqppzUpJxyZT3fZDcwseqCf0R/WnSWTPuvqTUUaTxrBckt2MLNax4l4/ZPiIhNbTiNyw6SlDaD2XjL2/T5fBo1xQc8p89qlUzSDRPY5b08yzf8X+K4r6KEdtHfEyHHYZBmR6bFZdksK9zL97e+x+qiIxSXZV3e1og+gz6w4x0sHDKT7ksnUVG0z81eBupEMSdMI/gkM8vfZJu2lnR32Qk6Ru8nVN6OgqJNYkrKK3OZnbWBb0e9xSlVcqBwN02err8UoG/d+P11T/F0j6l0W3wH1rKjXgtiRBXZ5lHMOb6r3nR84IfR4H6ssTuZgbGsHON1rHG9M82XGKTUjzV+g1p7HVNWPeR1EpoEy495rDHAW5kfcUIZh81Q5fTBIqH2FDcvGsvh8hwypnxDYvxA3eN3iU6FSen0s/dMvpnjU77is+zvmPL5XfpJaK731BmKyQ4a+UNIhvMVtfuSBtLWtoQAGeO54BxaterHyuGvakfPb/y7dujVpeaIkkBoa+bc8DwdIxIYtur3VBfv92yFUkKt8byPnj+/Ke53szaSKQdQrezzMO8EFBRspce8wWRXFpA9bT2Tej8ESrBLin4SOEZ5EkzhPNz/abImrearvG1cv2CYTrLbSh4JVBk3cTT4uvMhGc7njH8H9lSUEGueT1BgPIGyO46Dr4QA1c6OvC3MOvQ5UzuM4a0bn6dUmNh35jCotXjc1XIx4TBJJRAUy+9+8TCfjnyNjJIsbv/yt+zP26AFEm7OC1XYKTG+TpppKkuPNLzl1w9cyGoKHkqaRAvba5jVZueoCQmEJ/J8n+nc1UG78ObF9PmUnkqj4cWP5wkpQSi+L7ypLvQRHqgTl+iFN+7QrnB6nUjbrTgmDsDVFwoJpoj6r3BS3Tb6OA6fbfRmN+maZnPE0a9wGp88jKkpwz2vcLJXeYVHb4HYKTPOJ8P8xKV8hZMnPC4l03WEO2EO4h2XkiUPpV9sF/aeOcb6wl1sL8pgT+kxbat0XRn1X0qmQFC0f5eSOWusl8PjFjjjTvKUpy6fS8nccc41ew1IpgQMJqJjujmv2esSmUzHiLb6NXsWztpqz7lmz2wwcrqmjCPlJ5pwzR4Oyf8ZXLPnDu+LI50qRfroEB12uZvkC9HAxZE+bjZ19GleJqfb55/ZxZHnwsB97QcRY5tGkPorAtWwH7EMkhpDCbWGReQx9+d5FaovOC/3tY8iQAwjSI3DIIVLbzrMraZA583R0V3Bl/vWB/26amUAgfbePq+rdu/E0D+7FvRIbMJOneEMKoewsZ8qrl5X7Sc8L2APNgZil7EEq1qXZVVUhHqKKqWOSllAbk0h2/NLuCSdKvD/SHkz5b6TTjQAAAAASUVORK5CYII="));
}

jstring IconWebViewData(JNIEnv *env, jobject thiz) {
    iconValid = true;
    //WebView support GIF animation. Upload your image or GIF on imgur.com or other sites

    // From internet (Requires android.permission.INTERNET)
    // return env->NewStringUTF(OBFUSCATE("https://system.cheat-aov.net/icon/menu.gif"));

    // Base64 html:
    // return env->NewStringUTF("data:image/png;base64, <encoded base64 here>");

    // To disable it, return NULL. It will use normal image above:
    // return NULL

    //return env->NewStringUTF(OBFUSCATE_KEY("https://i.imgur.com/SujJ85j.gif", 'u'));
    return NULL;
}

jobjectArray SettingsList(JNIEnv *env, jobject activityObject) {
    jobjectArray ret;

    const char *features[] = {
            OBFUSCATE("Category_Cài Đặt"),
          //  OBFUSCATE("-1_Toggle_Load Setting Cũ"), //-1 is checked on Preferences.java
         //   OBFUSCATE("-3_Toggle_Thu Nhỏ Kích Thước Menu"),
          //  OBFUSCATE("-6_Button_<font color='red'>Đóng Cài Đặt</font>"),
    };

    int Total_Feature = (sizeof features /
                         sizeof features[0]); //Now you dont have to manually update the number everytime;
    ret = (jobjectArray)
            env->NewObjectArray(Total_Feature, env->FindClass(OBFUSCATE("java/lang/String")),
                                env->NewStringUTF(""));
    int i;
    for (i = 0; i < Total_Feature; i++)
        env->SetObjectArrayElement(ret, i, env->NewStringUTF(features[i]));

    settingsValid = true;

    return (ret);
}
