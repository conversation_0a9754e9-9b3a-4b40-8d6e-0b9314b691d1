#ifndef MONOSTRING_H
#define MONOSTRING_H

#include <string>
#include <stdint.h>

#ifdef __GNUC__
#include <endian.h>
#endif

using namespace std;

class MonoString {
    void *klass;
    void *monitor;
    int length;
    char chars[1];
    char *getChars() {
        return chars;
    }

public:
    int getLength() {
        return length;
    }
	
    const char *toChars();
	
    string toString();

    void setMonoString(const char *s);

    void setMonoString(string s);
};

std::string utf16_to_utf8(const std::u16string &u16str);

std::string utf16le_to_utf8(const std::u16string &u16str);

std::string utf16be_to_utf8(const std::u16string &u16str);

std::u16string utf8_to_utf16le(const std::string &u8str, bool addbom = false, bool *ok = NULL);

std::u16string utf8_to_utf16be(const std::string &u8str, bool addbom = false, bool *ok = NULL);

#endif //MONOSTRING_H

