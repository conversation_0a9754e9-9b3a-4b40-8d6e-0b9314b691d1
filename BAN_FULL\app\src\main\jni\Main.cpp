#include <unistd.h>
#include <fstream>
#include <iostream>
#include <dlfcn.h>
#include "Includes/Logger.h"
#include "Includes/obfuscate.h"
#include "Includes/Utils.h"
#include "KittyMemory/MemoryPatch.h"
#include "Menu/Setup.h"
#include "Tools/Tools.h"
#include "IL2CppSDKGenerator/IL2Cpp.h"

#define targetLibName OBFUSCATE("libil2cpp.so")
#define MOD_LIBNAME OBFUSCATE("libVN.so")
#define GAME_CODE "aovvn"
#define MOD_VERSION "VN64-2904"
#define PhienBan ("Chơ<PERSON>ín")
#include "Includes/Macros.h"
#include <sys/stat.h>
#include "AES/AES.cpp"
uintptr_t il2cppMap;

#include "IL2CppSDKGenerator/Quaternion.hpp"
#include "IL2CppSDKGenerator/Vector3.hpp"
#include "IL2CppSDKGenerator/VInt3.hpp"
#include "ARMPatch.cpp"
#include "StrEnc.h"
#include "zip.h"
#include <curl/curl.h>
#include "json.hpp"
#include "LicenseTools.h"
#include <openssl/evp.h>
#include <openssl/pem.h>
#include <openssl/rsa.h>
#include <openssl/err.h>
#include <openssl/md5.h>
#include <sstream>
#include <iomanip>

using json = nlohmann::ordered_json;
using namespace std;
#include <fstream>
#include "base64/base64.cpp"


#include "TH/AntiCrack.h"

struct My_Patches { 
    
    
} hexPatches;



#include "TH/hooker.h"




void *hack_thread(void *) {
    while (!il2cppMap) {
        il2cppMap = Tools::GetBaseAddress("libil2cpp.so");
        sleep(1);
    }
    
    IL2Cpp::Il2CppAttach();
    
    #include "TH/hooks.h"
    
    return NULL;
}

jobjectArray GetFeatureList(JNIEnv *env, jobject context) {
        if (!isAuthenticated() || !CheckModVersion()) return NULL;
    jobjectArray ret;

    const char *features[] = {            
        OBFUSCATE("tab1_Category_ "),
        OBFUSCATE("tab1_1_Toggle_Hack Map"),             
        OBFUSCATE("tab1_2_SeekBar_Wide View_0_50"),
        
          
        OBFUSCATE("tab2_Category_ "),
        OBFUSCATE("tab2_9_Toggle_Kích hoạt AIM Skill ELSU"),
        OBFUSCATE("tab2_10_RadioButton_Mục Tiêu AIM_Máu thấp nhất,%Máu thấp nhất,Khoảng cách gần nhất"),
        
       OBFUSCATE("tab3_Category_ "),
        OBFUSCATE("tab3_100_ButtonOnOff_Tự Động Dùng Bổ Trợ"),      
        OBFUSCATE("tab3_101_SeekBar_Số % Máu Sẽ Dùng Bổ Trợ_20_50"),
        OBFUSCATE("tab3_102_Toggle_Tự Động Dùng Bộc Phá"),     
    };

    int Total_Feature = (sizeof features / sizeof features[0]);
    
    jclass stringClass = env->FindClass(OBFUSCATE("java/lang/String"));
    if (!stringClass) return nullptr;
    
    ret = (jobjectArray)env->NewObjectArray(Total_Feature, stringClass, env->NewStringUTF(""));
    if (!ret) return nullptr;

    for (int i = 0; i < Total_Feature; i++) {
        jstring featureString = env->NewStringUTF(features[i]);
        if (featureString) {
            env->SetObjectArrayElement(ret, i, featureString);
            env->DeleteLocalRef(featureString);
        }
    }

    return ret;
}

jobjectArray GetCategoryList(JNIEnv *env, jobject /* context */) {
    const char *categories[] = {
        OBFUSCATE("Map + Camera"),
        OBFUSCATE("AIM Skill"),
        OBFUSCATE("Công Cụ")
    };
    
    int size = sizeof(categories) / sizeof(categories[0]);
    
    jclass stringClass = env->FindClass(OBFUSCATE("java/lang/String"));
    if (!stringClass) return nullptr;
    
    jobjectArray ret = (jobjectArray)env->NewObjectArray(size, stringClass, env->NewStringUTF(""));
    if (!ret) return nullptr;
    
    for (int i = 0; i < size; i++) {
        jstring categoryString = env->NewStringUTF(categories[i]);
        if (categoryString) {
            env->SetObjectArrayElement(ret, i, categoryString);
            env->DeleteLocalRef(categoryString);
        }
    }
    
    return ret;
}

void Changes(JNIEnv *env, jclass clazz, jobject obj, jint featNum, jstring featName, jint value, jboolean boolean, jstring str) {
     if (!isAuthenticated() || !CheckModVersion()) {
        return;
    }
    
    switch (featNum) {
        case 1: 
            HackMap = boolean;
            break; 
        case 2:
            WideView.SetFieldOfView = (float) value * 0.0362f;
            WideView.Active = true;
           break;
           
           case 6:
                unlockskin = boolean;
                break;
                
          case 9:
            AimElsu = boolean;
            break;
        case 10:
             switch (value) {
             case 1:
                aimType = 1;
                break;
             case 2:
                aimType = 0;
                break;
             case 3:
                aimType = 2;
                break;
             }
             break;
          
    case 100:
            AutoBoTro = boolean;
            break;
         case 101:
            PhanTramHP = value;
            break;
       case 102:
           AutoPhuTro = boolean;
           break;
    }
}

__attribute__((constructor))
void lib_main() {
}

int RegisterMenu(JNIEnv *env) {
    JNINativeMethod methods[] = {
            {OBFUSCATE("Icon"), OBFUSCATE("()Ljava/lang/String;"), reinterpret_cast<void *>(Icon)},
            {OBFUSCATE("IconWebViewData"), OBFUSCATE("()Ljava/lang/String;"), reinterpret_cast<void *>(IconWebViewData)},
            {OBFUSCATE("IsGameLibLoaded"), OBFUSCATE("()Z"), reinterpret_cast<void *>(isGameLibLoaded)},
            {OBFUSCATE("Init"), OBFUSCATE("(Landroid/content/Context;Landroid/widget/TextView;Landroid/widget/TextView;)V"), reinterpret_cast<void *>(Init)},
            {OBFUSCATE("SettingsList"), OBFUSCATE("()[Ljava/lang/String;"), reinterpret_cast<void *>(SettingsList)},
            {OBFUSCATE("GetCategoryList"), OBFUSCATE("()[Ljava/lang/String;"), reinterpret_cast<void *>(GetCategoryList)},
    };

    jclass clazz = env->FindClass(OBFUSCATE("com/hmod/vip/Menu"));
    if (!clazz) return JNI_ERR;
    
    if (env->RegisterNatives(clazz, methods, sizeof(methods) / sizeof(methods[0])) != 0) {
        env->DeleteLocalRef(clazz);
        return JNI_ERR;
    }
    
    env->DeleteLocalRef(clazz);
    return JNI_OK;
}

int RegisterFt(JNIEnv *env) {
    if (!isAuthenticated()) return JNI_ERR;
    
    pthread_t ptid;
    pthread_create(&ptid, NULL, hack_thread, NULL);
    
    JNINativeMethod methods[] = {
        {OBFUSCATE("GetFeatureList"), OBFUSCATE("()[Ljava/lang/String;"), reinterpret_cast<void *>(GetFeatureList)},
    };

    jclass clazz = env->FindClass(OBFUSCATE("com/hmod/vip/Menu"));
    if (!clazz) return JNI_ERR;
    
    if (env->RegisterNatives(clazz, methods, sizeof(methods) / sizeof(methods[0])) != 0) {
        env->DeleteLocalRef(clazz);
        return JNI_ERR;
    }
    
    env->DeleteLocalRef(clazz);
    return JNI_OK;
}

int RegisterPreferences(JNIEnv *env) {
    JNINativeMethod methods[] = {
        {OBFUSCATE("Changes"), OBFUSCATE("(Landroid/content/Context;ILjava/lang/String;IZLjava/lang/String;)V"), reinterpret_cast<void *>(Changes)},
    };
    
    jclass clazz = env->FindClass(OBFUSCATE("com/hmod/vip/Preferences"));
    if (!clazz) return JNI_ERR;
    
    if (env->RegisterNatives(clazz, methods, sizeof(methods) / sizeof(methods[0])) != 0) {
        env->DeleteLocalRef(clazz);
        return JNI_ERR;
    }
    
    env->DeleteLocalRef(clazz);
    return JNI_OK;
}

int RegisterMain(JNIEnv *env) {
    JNINativeMethod methods[] = {
        {OBFUSCATE("CheckOverlayPermission"), OBFUSCATE("(Landroid/content/Context;)V"), reinterpret_cast<void *>(CheckOverlayPermission)},
    };
    
    jclass clazz = env->FindClass(OBFUSCATE("com/hmod/vip/Main"));
    if (!clazz) return JNI_ERR;
    
    if (env->RegisterNatives(clazz, methods, sizeof(methods) / sizeof(methods[0])) != 0) {
        env->DeleteLocalRef(clazz);
        return JNI_ERR;
    }
    
    env->DeleteLocalRef(clazz);
    return JNI_OK;
}

std::string jstringToString(JNIEnv* env, jstring jstr) {
    if (!jstr) return "";
    
    const char* strChars = env->GetStringUTFChars(jstr, NULL);
    if (!strChars) return "";
    
    std::string str(strChars);
    env->ReleaseStringUTFChars(jstr, strChars);
    return str;
}

extern "C" {

JNIEXPORT jstring JNICALL
Java_com_hmod_vip_Menu_Check(JNIEnv *env, jclass clazz, jobject mContext, jstring mUserKey) {
   if(loginAttempts >= MAX_LOGIN_ATTEMPTS) {
       return env->NewStringUTF(OBFUSCATE("Quá nhiều lần đăng nhập sai. Vui lòng đóng trò chơi và mở lại để có thể đăng nhập."));
   }
   
   // Kiểm tra tham số đầu vào
   if (mUserKey == nullptr || mContext == nullptr) {
       return env->NewStringUTF(OBFUSCATE("Lỗi: Tham số đầu vào không hợp lệ"));
   }
   
   // Lấy thông tin signature và MD5 để gửi lên server
   std::string appSignature = checkSignature(env, mContext);
   std::string apkMd5 = checkBaseApkMD5(env, mContext);
   
   // Đặt flag mặc định về false
   secValue2 = secValue1; // Đảm bảo xác thực không hợp lệ
   g_Token.clear();
   g_Auth.clear();
   
   const char* userKey = nullptr;
   try {
       userKey = env->GetStringUTFChars(mUserKey, 0);
       if (!userKey) {
           return env->NewStringUTF(OBFUSCATE("Lỗi khi đọc key"));
       }
   } catch(...) {
       return env->NewStringUTF(OBFUSCATE("Lỗi ngoại lệ khi đọc key"));
   }
   
   // Lấy thông tin cơ bản về thiết bị
   jclass buildClass = nullptr;
   jclass buildVersionClass = nullptr;
   jstring jModel = nullptr;
   jstring jManufacturer = nullptr;
   jstring jRelease = nullptr;
   jstring jFingerprint = nullptr;
   const char* modelStr = nullptr;
   const char* manufacturerStr = nullptr;
   const char* releaseStr = nullptr;
   const char* fingerprintStr = nullptr;
   std::string deviceInfo = "Unknown Device";
   
   try {
       buildClass = env->FindClass(OBFUSCATE("android/os/Build"));
       if (!buildClass) {
           if (userKey) env->ReleaseStringUTFChars(mUserKey, userKey);
           return env->NewStringUTF(OBFUSCATE("Lỗi khi lấy thông tin thiết bị"));
       }
       
       buildVersionClass = env->FindClass(OBFUSCATE("android/os/Build$VERSION"));
       if (!buildVersionClass) {
           env->DeleteLocalRef(buildClass);
           if (userKey) env->ReleaseStringUTFChars(mUserKey, userKey);
           return env->NewStringUTF(OBFUSCATE("Lỗi khi lấy thông tin phiên bản Android"));
       }

       // Lấy tên model thiết bị
       jfieldID modelId = env->GetStaticFieldID(buildClass, OBFUSCATE("MODEL"), OBFUSCATE("Ljava/lang/String;"));
       if (modelId) {
           jModel = (jstring)env->GetStaticObjectField(buildClass, modelId);
           if (jModel) {
               modelStr = env->GetStringUTFChars(jModel, NULL);
           }
       }

       // Lấy tên nhà sản xuất
       jfieldID manufacturerId = env->GetStaticFieldID(buildClass, OBFUSCATE("MANUFACTURER"), OBFUSCATE("Ljava/lang/String;"));
       if (manufacturerId) {
           jManufacturer = (jstring)env->GetStaticObjectField(buildClass, manufacturerId);
           if (jManufacturer) {
               manufacturerStr = env->GetStringUTFChars(jManufacturer, NULL);
           }
       }

       // Lấy phiên bản Android
       jfieldID releaseId = env->GetStaticFieldID(buildVersionClass, OBFUSCATE("RELEASE"), OBFUSCATE("Ljava/lang/String;"));
       if (releaseId) {
           jRelease = (jstring)env->GetStaticObjectField(buildVersionClass, releaseId);
           if (jRelease) {
               releaseStr = env->GetStringUTFChars(jRelease, NULL);
           }
       }

       // Lấy phiên bản Build (Firmware/Software version)
       jfieldID fingerprintId = env->GetStaticFieldID(buildClass, OBFUSCATE("FINGERPRINT"), OBFUSCATE("Ljava/lang/String;"));
       if (fingerprintId) {
           jFingerprint = (jstring)env->GetStaticObjectField(buildClass, fingerprintId);
           if (jFingerprint) {
               fingerprintStr = env->GetStringUTFChars(jFingerprint, NULL);
           }
       }

       // Tạo chuỗi thông tin thiết bị đầy đủ
       char deviceInfoBuffer[1024] = {0};
       snprintf(deviceInfoBuffer, sizeof(deviceInfoBuffer) - 1, "%s %s (Android %s, Build: %s)",
               manufacturerStr ? manufacturerStr : "Unknown",
               modelStr ? modelStr : "Device",
               releaseStr ? releaseStr : "Unknown",
               fingerprintStr ? fingerprintStr : "Unknown");

       deviceInfo = std::string(deviceInfoBuffer);
   } catch (...) {
       // Ignore errors, use default device info
   }
   
   // Tạo hwid từ thông tin người dùng và thiết bị
   std::string hwid;
   std::string UUID;
   
   try {
       hwid = userKey;
       hwid += GetAndroidID(env, mContext);
       hwid += GetDeviceModel(env);
       hwid += GetDeviceBrand(env);

       // Tạo UUID từ hwid
       UUID = GetDeviceUniqueIdentifier(env, hwid.c_str());
       
       if (UUID.empty()) {
           if (userKey) env->ReleaseStringUTFChars(mUserKey, userKey);
           
           // Giải phóng tài nguyên
           if (modelStr && jModel) env->ReleaseStringUTFChars(jModel, modelStr);
           if (manufacturerStr && jManufacturer) env->ReleaseStringUTFChars(jManufacturer, manufacturerStr);
           if (releaseStr && jRelease) env->ReleaseStringUTFChars(jRelease, releaseStr);
           if (fingerprintStr && jFingerprint) env->ReleaseStringUTFChars(jFingerprint, fingerprintStr);
           
           env->DeleteLocalRef(buildVersionClass);
           env->DeleteLocalRef(buildClass);
           
           return env->NewStringUTF(OBFUSCATE("Không thể tạo ID thiết bị"));
       }
   } catch (...) {
       if (userKey) env->ReleaseStringUTFChars(mUserKey, userKey);
       
       // Giải phóng tài nguyên
       if (modelStr && jModel) env->ReleaseStringUTFChars(jModel, modelStr);
       if (manufacturerStr && jManufacturer) env->ReleaseStringUTFChars(jManufacturer, manufacturerStr);
       if (releaseStr && jRelease) env->ReleaseStringUTFChars(jRelease, releaseStr);
       if (fingerprintStr && jFingerprint) env->ReleaseStringUTFChars(jFingerprint, fingerprintStr);
       
       env->DeleteLocalRef(buildVersionClass);
       env->DeleteLocalRef(buildClass);
       
       return env->NewStringUTF(OBFUSCATE("Lỗi khi tạo ID thiết bị"));
   }
   
   // Giải phóng tài nguyên của thông tin thiết bị
   if (modelStr && jModel) env->ReleaseStringUTFChars(jModel, modelStr);
   if (manufacturerStr && jManufacturer) env->ReleaseStringUTFChars(jManufacturer, manufacturerStr);
   if (releaseStr && jRelease) env->ReleaseStringUTFChars(jRelease, releaseStr);
   if (fingerprintStr && jFingerprint) env->ReleaseStringUTFChars(jFingerprint, fingerprintStr);
   
   env->DeleteLocalRef(buildVersionClass);
   env->DeleteLocalRef(buildClass);
   
   std::string errMsg = "Lỗi kết nối server";
   std::string out;

   struct MemoryStruct chunk{};
   chunk.memory = (char *) malloc(1);
   if (!chunk.memory) {
       if (userKey) env->ReleaseStringUTFChars(mUserKey, userKey);
       return env->NewStringUTF(OBFUSCATE("Lỗi cấp phát bộ nhớ"));
   }
   chunk.size = 0;

   CURL *curl = curl_easy_init();
   if (curl) {
       curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "POST");
       curl_easy_setopt(curl, CURLOPT_URL, std::string(OBFUSCATE("https://hmod.io.vn/public/login-app.php")).c_str());
       curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
       curl_easy_setopt(curl, CURLOPT_DEFAULT_PROTOCOL, "https");
       
       
       struct curl_slist *headers = NULL;
       headers = curl_slist_append(headers, "Content-Type: application/x-www-form-urlencoded");
       curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);

       char data[4096] = {0};
       snprintf(data, sizeof(data) - 1, 
           OBFUSCATE("game=lqm&key=%s&deviceid=%s&version=%s&gamecode=%s&device_info=%s&mod_description=%s&apk_md5=%s&signature=%s"), 
           userKey, 
           UUID.c_str(), 
           MOD_VERSION, 
           GAME_CODE, 
           deviceInfo.c_str(), 
           PhienBan, 
           apkMd5.c_str(), 
           appSignature.c_str());
       
       curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data);
       curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteMemoryCallback);
       curl_easy_setopt(curl, CURLOPT_WRITEDATA, (void *) &chunk);
       curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
       curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);

       CURLcode res = curl_easy_perform(curl);
       if (res == CURLE_OK) {
           if (chunk.memory && chunk.size > 0) {
               // Đảm bảo chuỗi kết thúc bằng NULL
               if (chunk.memory[chunk.size - 1] != '\0') {
                   char *ptr = (char*)realloc(chunk.memory, chunk.size + 1);
                   if (ptr) {
                       chunk.memory = ptr;
                       chunk.memory[chunk.size] = '\0';
                   } else {
                       if (userKey) env->ReleaseStringUTFChars(mUserKey, userKey);
                       free(chunk.memory);
                       curl_slist_free_all(headers);
                       curl_easy_cleanup(curl);
                       return env->NewStringUTF(OBFUSCATE("Lỗi cấp phát bộ nhớ khi xử lý phản hồi"));
                   }
               }
               
               try {
                   json result = json::parse(chunk.memory);
                   if (result.contains("bool") && result["bool"] == "true") {
                       if (result.contains("token") && result["token"].is_string()) {
                           std::string token = result["token"].get<std::string>();
                           std::string auth = "lqm-" + std::string(userKey) + "-" + UUID + "-Vm8Lk7Uj2JmsjCPVPVjrLa7zgfx3uz9E";
                           std::string outputAuth = CalcMD5(auth);
                           
                           g_Token = token;
                           g_Auth = outputAuth;
                           
                           if(g_Token == g_Auth) {
                               if(!CheckModVersion()) {
                                   // Đảm bảo xác thực không hợp lệ
                                   secValue2 = secValue1;
                                   free(chunk.memory);
                                   if (userKey) env->ReleaseStringUTFChars(mUserKey, userKey);
                                   curl_slist_free_all(headers);
                                   curl_easy_cleanup(curl);
                                   return env->NewStringUTF(OBFUSCATE("Phiên bản mod không trùng khớp với dữ liệu trên server. Vui lòng cập nhật!"));
                               }
                               
                               // Thiết lập trạng thái xác thực hợp lệ
                               secValue2 = secValue1 ^ 0x12345678;
                               
                               loginAttempts = 0;
                               out = StrEnc("T#y?xVp?a~`Jhj{>]7", "\x2F\x01\x0B\x5A\x0B\x74\x4A\x1D\x2E\x35\x42\x66\x4A\x02\x1F\x1C\x67\x15", 18).c_str();
                               
                               json responseObj;
                               responseObj["res"] = "OK";
                               
                               if (result.contains("user") && result["user"].is_string()) {
                                   responseObj["user"] = result["user"].get<std::string>();
                               }
                               
                               std::string userInfo;
                               if (result.contains("o") && result["o"].is_object() && 
                                   result["o"].contains("hsd") && result["o"]["hsd"].is_string()) {
                                   userInfo = "HSD : " + result["o"]["hsd"].get<std::string>();
                               } else {
                                   userInfo = "HSD : Unknown";
                               }
                               
                               std::string encodedInfo = base64_encode(userInfo);
                               responseObj["hd"] = encodedInfo;
                               
                               if (encodedInfo.length() >= 10) {
                                   // Chuyển đổi JSON object thành chuỗi để trả về
                                   out = responseObj.dump();
                                   RegisterFt(env);
                               } else {
                                   // Đảm bảo xác thực không hợp lệ
                                   secValue2 = secValue1;
                                   free(chunk.memory);
                                   if (userKey) env->ReleaseStringUTFChars(mUserKey, userKey);
                                   curl_slist_free_all(headers);
                                   curl_easy_cleanup(curl);
                                   return env->NewStringUTF(OBFUSCATE("Có vấn đề về KEY của bạn, vui lòng liên hệ Admin để biết thêm thông tin!"));
                               }
                           } else {
                               // Token không hợp lệ, đảm bảo xác thực không hợp lệ
                               secValue2 = secValue1;
                           }
                       } else {
                           secValue2 = secValue1; // Đảm bảo xác thực không hợp lệ
                           errMsg = "Không nhận được token từ server";
                       }
                   } else if (result.contains("mes") && result["mes"].is_string()) {
                       loginAttempts++;
                       errMsg = result["mes"].get<std::string>();
                   } else {
                       loginAttempts++;
                       errMsg = "Phản hồi không hợp lệ từ server";
                   }
               } catch (const json::parse_error& e) {
                   loginAttempts++;
                   errMsg = "Lỗi xử lý dữ liệu: Không phải định dạng JSON hợp lệ";
               } catch (std::exception &e) {
                   loginAttempts++;
                   errMsg = "Lỗi xử lý dữ liệu";
               } catch (...) {
                   loginAttempts++;
                   errMsg = "Lỗi không xác định khi xử lý dữ liệu";
               }
           } else {
               loginAttempts++;
               errMsg = "Không nhận được dữ liệu từ server";
           }
       } else {
           loginAttempts++;
           errMsg = curl_easy_strerror(res);
       }

       curl_slist_free_all(headers);
       curl_easy_cleanup(curl);
   } else {
       loginAttempts++;
       errMsg = "Không thể khởi tạo kết nối";
   }

   if (chunk.memory) {
       free(chunk.memory);
   }

   if (userKey) {
       env->ReleaseStringUTFChars(mUserKey, userKey);
   }
   
   return isAuthenticated() ? env->NewStringUTF(out.c_str()) : env->NewStringUTF(errMsg.c_str());
}

JNIEXPORT jint JNICALL
JNI_OnLoad(JavaVM *vm, void *reserved) {
    loginAttempts = 0;
    
    // Khởi tạo giá trị bảo mật
    srand(time(NULL));
    secValue1 = 0xA5A5A5A5 ^ (rand() & 0xFFFF); // Tạo giá trị cơ sở ngẫu nhiên
    secValue2 = secValue1; // Đảm bảo xác thực không hợp lệ lúc khởi động
    
    g_Token.clear();
    g_Auth.clear();
    
    JNIEnv *env;
    if (vm->GetEnv((void **) &env, JNI_VERSION_1_6) != JNI_OK) {
        return JNI_ERR;
    }
    
    if (RegisterMenu(env) != 0) return JNI_ERR;
    if (RegisterPreferences(env) != 0) return JNI_ERR;
    if (RegisterMain(env) != 0) return JNI_ERR;
    
    return JNI_VERSION_1_6;
}

} // extern "C"

