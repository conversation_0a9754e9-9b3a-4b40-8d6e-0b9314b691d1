<?php
// B<PERSON>t hiển thị lỗi để debug
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Bật log lỗi
ini_set('log_errors', 1);
ini_set('error_log', dirname(__FILE__) . '/php_errors.log');

// Kiểm tra nếu session chưa được khởi tạo thì mới gọi session_start()
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

include_once("config/config.php");

// Kiểm tra đăng nhập admin
if (!isset($_SESSION["level"]) || $_SESSION["level"] != "admin") {
    echo '<script>alert("Bạn không có quyền truy cập trang này!"); window.location.href="../main/";</script>';
    exit;
}

// Kiểm tra bảng version_config có tồn tại không
$tableExists = false;
$result = $con->query("SHOW TABLES LIKE 'version_config'");
if ($result && $result->num_rows > 0) {
    $tableExists = true;
}

// Nếu bảng chưa tồn tại, tạo bảng với các cột mở rộng cho MOD
if (!$tableExists) {
    $sql = "CREATE TABLE `version_config` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `version` varchar(50) NOT NULL,
      `apk_md5` varchar(255) NOT NULL,
      `signature` varchar(255) NOT NULL,
      `game_code` varchar(50) NOT NULL,
      `lib_name` varchar(255) DEFAULT NULL,
      `description` text DEFAULT NULL,
      `safety` enum('safe','unsafe') DEFAULT 'safe',
      `safety_message` text DEFAULT NULL,
      `icon` varchar(50) DEFAULT NULL,
      `title` varchar(255) DEFAULT NULL,
      `status` enum('ready','not_ready') DEFAULT 'not_ready',
      `file_path` varchar(255) DEFAULT NULL,
      `vpn_check_enabled` tinyint(1) NOT NULL DEFAULT 1,
      `is_active` tinyint(1) NOT NULL DEFAULT 1,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
      PRIMARY KEY (`id`),
      UNIQUE KEY `version_game_code` (`version`, `game_code`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

    if ($con->query($sql)) {
        $success_message = "Bảng version_config đã được tạo thành công!";
    } else {
        $error_message = "Lỗi khi tạo bảng: " . $con->error;
    }
}

// Kiểm tra bảng mod_app_mapping có tồn tại không
$result = $con->query("SHOW TABLES LIKE 'mod_app_mapping'");
if ($result->num_rows == 0) {
    // Tạo bảng mod_app_mapping nếu chưa tồn tại
    $sql = "CREATE TABLE `mod_app_mapping` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `version_config_id` int(11) NOT NULL,
      `app_identifier` varchar(100) NOT NULL,
      PRIMARY KEY (`id`),
      UNIQUE KEY `version_app` (`version_config_id`, `app_identifier`),
      CONSTRAINT `fk_version_config` FOREIGN KEY (`version_config_id`) REFERENCES `version_config` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

    if ($con->query($sql)) {
        $success_message = isset($success_message) ? $success_message . " Bảng mod_app_mapping đã được tạo thành công!" : "Bảng mod_app_mapping đã được tạo thành công!";
    } else {
        $error_message = isset($error_message) ? $error_message . " Lỗi khi tạo bảng mod_app_mapping: " . $con->error : "Lỗi khi tạo bảng mod_app_mapping: " . $con->error;
    }
}

if ($tableExists) {
    // Kiểm tra và thêm các cột MOD nếu chưa có
    $columnsToAdd = [
        "lib_name" => "ADD COLUMN `lib_name` varchar(255) DEFAULT NULL AFTER `game_code`",
        "safety" => "ADD COLUMN `safety` enum('safe','unsafe') DEFAULT 'safe' AFTER `description`",
        "safety_message" => "ADD COLUMN `safety_message` text DEFAULT NULL AFTER `safety`",
        "icon" => "ADD COLUMN `icon` varchar(50) DEFAULT NULL AFTER `safety_message`",
        "title" => "ADD COLUMN `title` varchar(255) DEFAULT NULL AFTER `icon`",
        "status" => "ADD COLUMN `status` enum('ready','not_ready') DEFAULT 'not_ready' AFTER `title`",
        "file_path" => "ADD COLUMN `file_path` varchar(255) DEFAULT NULL AFTER `status`",
        "vpn_check_enabled" => "ADD COLUMN `vpn_check_enabled` tinyint(1) NOT NULL DEFAULT 1 AFTER `file_path`"
    ];

    foreach ($columnsToAdd as $column => $addStatement) {
        $checkColumn = $con->query("SHOW COLUMNS FROM `version_config` LIKE '$column'");
        if ($checkColumn->num_rows == 0) {
            $alterSql = "ALTER TABLE `version_config` $addStatement";
            $con->query($alterSql);
        }
    }
}

// Xử lý tải lên file MOD
if (isset($_POST['upload_mod'])) {
    $id = mysqli_real_escape_string($con, $_POST['mod_id']);

    // Kiểm tra id có tồn tại không
    $check_sql = "SELECT id, lib_name FROM version_config WHERE id = $id";
    $check_result = $con->query($check_sql);

    if ($check_result->num_rows > 0) {
        $row = $check_result->fetch_assoc();
        $lib_name = $row['lib_name'];

        // Kiểm tra xem lib_name có được thiết lập không
        if (empty($lib_name)) {
            $error_message = "Tên thư viện (lib_name) chưa được thiết lập!";
        } else {
            // Xử lý tải lên file
            if (isset($_FILES['mod_file']) && $_FILES['mod_file']['error'] == 0) {
                // Tạo thư mục files nếu chưa tồn tại
                if (!file_exists("files")) {
                    mkdir("files", 0755, true);
                }

                $target_dir = "files/";
                $target_file = $target_dir . $lib_name;

                if (move_uploaded_file($_FILES['mod_file']['tmp_name'], $target_file)) {
                    $file_path = "files/" . $lib_name;
                    chmod($target_file, 0644); // Đặt quyền cho file

                    // Cập nhật trạng thái thành ready
                    $sql = "UPDATE version_config SET status = 'ready', file_path = '$file_path' WHERE id = $id";

                    if ($con->query($sql)) {
                        $success_message = "Tải lên file MOD thành công!";
                    } else {
                        $error_message = "Lỗi khi cập nhật trạng thái: " . $con->error;
                    }
                } else {
                    $error_message = "Lỗi khi tải file lên!";
                }
            } else {
                $error_message = "Vui lòng chọn file để tải lên!";
            }
        }
    } else {
        $error_message = "ID không hợp lệ!";
    }
}

// Thay đổi đoạn xử lý thêm phiên bản mới
if (isset($_POST['add_version'])) {
    $version = mysqli_real_escape_string($con, $_POST['version']);
    $apk_md5 = mysqli_real_escape_string($con, strtolower($_POST['apk_md5'])); // Chuyển thành chữ thường
    $signature = mysqli_real_escape_string($con, strtolower($_POST['signature'])); // Chuyển thành chữ thường
    $game_code = mysqli_real_escape_string($con, $_POST['game_code']);
    $description = mysqli_real_escape_string($con, $_POST['description']);
    $is_active = isset($_POST['is_active']) ? 1 : 0;

    // Lấy thông tin MOD nếu được cung cấp
    $lib_name = isset($_POST['lib_name']) ? mysqli_real_escape_string($con, $_POST['lib_name']) : null;
    $safety = isset($_POST['safety']) ? mysqli_real_escape_string($con, $_POST['safety']) : 'safe';
    $safety_message = isset($_POST['safety_message']) ? mysqli_real_escape_string($con, $_POST['safety_message']) : null;
    $icon = isset($_POST['icon']) ? mysqli_real_escape_string($con, $_POST['icon']) : null;
    $title = isset($_POST['title']) ? mysqli_real_escape_string($con, $_POST['title']) : null;
    $status = isset($_POST['status']) ? mysqli_real_escape_string($con, $_POST['status']) : 'not_ready';
    $vpn_check_enabled = isset($_POST['vpn_check_enabled']) ? 1 : 0;

    // Kiểm tra phiên bản đã tồn tại chưa
    $check_sql = "SELECT * FROM version_config WHERE version = '$version' AND game_code = '$game_code'";
    $check_result = $con->query($check_sql);

    if ($check_result->num_rows > 0) {
        $error_message = "Phiên bản này đã tồn tại cho game này!";
    } else {
        $sql = "INSERT INTO version_config (version, apk_md5, signature, game_code, lib_name, description, safety, safety_message, icon, title, status, vpn_check_enabled, is_active)
                VALUES ('$version', '$apk_md5', '$signature', '$game_code', " .
                ($lib_name ? "'$lib_name'" : "NULL") . ", '$description', '$safety', " .
                ($safety_message ? "'$safety_message'" : "NULL") . ", " .
                ($icon ? "'$icon'" : "NULL") . ", " .
                ($title ? "'$title'" : "NULL") . ", '$status', $vpn_check_enabled, $is_active)";

        if ($con->query($sql)) {
            $success_message = "Thêm phiên bản thành công!";

            // Lấy ID vừa thêm
            $new_id = $con->insert_id;

            // Lưu mối quan hệ với app_identifier
            if (isset($_POST['app_identifiers']) && is_array($_POST['app_identifiers'])) {
                $app_stmt = $con->prepare("INSERT INTO mod_app_mapping (version_config_id, app_identifier) VALUES (?, ?)");
                foreach ($_POST['app_identifiers'] as $app_id) {
                    $app_id = mysqli_real_escape_string($con, $app_id);
                    $app_stmt->bind_param("is", $new_id, $app_id);
                    $app_stmt->execute();
                }
                $app_stmt->close();
            }

            // Xử lý tải lên file MOD nếu có
            if (!empty($lib_name) && isset($_FILES['mod_file']) && $_FILES['mod_file']['error'] == 0) {
                // Tạo thư mục files nếu chưa tồn tại
                if (!file_exists("files")) {
                    mkdir("files", 0755, true);
                }

                $target_dir = "files/";
                $target_file = $target_dir . $lib_name;

                if (move_uploaded_file($_FILES['mod_file']['tmp_name'], $target_file)) {
                    $file_path = "files/" . $lib_name;
                    chmod($target_file, 0644); // Đặt quyền cho file

                    // Cập nhật trạng thái thành ready và đường dẫn file
                    $update_sql = "UPDATE version_config SET status = 'ready', file_path = '$file_path' WHERE id = $new_id";
                    $con->query($update_sql);

                    $success_message .= " Và tải lên file MOD thành công!";
                } else {
                    $error_message = "Thêm phiên bản thành công nhưng lỗi khi tải file lên!";
                }
            }
        } else {
            $error_message = "Lỗi: " . $con->error;
        }
    }
}

// Thay đổi đoạn xử lý cập nhật phiên bản
if (isset($_POST['update_version'])) {
    $id = mysqli_real_escape_string($con, $_POST['id']);
    $version = mysqli_real_escape_string($con, $_POST['version']);
    $apk_md5 = mysqli_real_escape_string($con, strtolower($_POST['apk_md5'])); // Chuyển thành chữ thường
    $signature = mysqli_real_escape_string($con, strtolower($_POST['signature'])); // Chuyển thành chữ thường
    $game_code = mysqli_real_escape_string($con, $_POST['game_code']);
    $description = mysqli_real_escape_string($con, $_POST['description']);
    $is_active = isset($_POST['is_active']) ? 1 : 0;

    // Lấy thông tin MOD nếu được cung cấp
    $lib_name = isset($_POST['lib_name']) ? mysqli_real_escape_string($con, $_POST['lib_name']) : null;
    $safety = isset($_POST['safety']) ? mysqli_real_escape_string($con, $_POST['safety']) : 'safe';
    $safety_message = isset($_POST['safety_message']) ? mysqli_real_escape_string($con, $_POST['safety_message']) : null;
    $icon = isset($_POST['icon']) ? mysqli_real_escape_string($con, $_POST['icon']) : null;
    $title = isset($_POST['title']) ? mysqli_real_escape_string($con, $_POST['title']) : null;
    $status = isset($_POST['status']) ? mysqli_real_escape_string($con, $_POST['status']) : 'not_ready';
    $vpn_check_enabled = isset($_POST['vpn_check_enabled']) ? 1 : 0;

    // Kiểm tra phiên bản đã tồn tại chưa (ngoại trừ bản ghi hiện tại)
    $check_sql = "SELECT * FROM version_config WHERE version = '$version' AND game_code = '$game_code' AND id != $id";
    $check_result = $con->query($check_sql);

    if ($check_result->num_rows > 0) {
        $error_message = "Phiên bản này đã tồn tại cho game này!";
    } else {
        // Lấy lib_name cũ để kiểm tra xem nó có thay đổi không
        $old_lib_name = "";
        $old_lib_result = $con->query("SELECT lib_name, file_path FROM version_config WHERE id = $id");
        if ($old_lib_result->num_rows > 0) {
            $old_lib_row = $old_lib_result->fetch_assoc();
            $old_lib_name = $old_lib_row['lib_name'];
            $old_file_path = $old_lib_row['file_path'];
        }

        $sql = "UPDATE version_config SET
                version = '$version',
                apk_md5 = '$apk_md5',
                signature = '$signature',
                game_code = '$game_code',
                lib_name = " . ($lib_name ? "'$lib_name'" : "NULL") . ",
                description = '$description',
                safety = '$safety',
                safety_message = " . ($safety_message ? "'$safety_message'" : "NULL") . ",
                icon = " . ($icon ? "'$icon'" : "NULL") . ",
                title = " . ($title ? "'$title'" : "NULL") . ",
                status = '$status',
                vpn_check_enabled = $vpn_check_enabled,
                is_active = $is_active
                WHERE id = $id";

        if ($con->query($sql)) {
            $success_message = "Cập nhật phiên bản thành công!";

            // Xóa các mối quan hệ cũ
            $delete_stmt = $con->prepare("DELETE FROM mod_app_mapping WHERE version_config_id = ?");
            $delete_stmt->bind_param("i", $id);
            $delete_stmt->execute();
            $delete_stmt->close();

            // Lưu mối quan hệ mới với app_identifier
            if (isset($_POST['app_identifiers']) && is_array($_POST['app_identifiers'])) {
                $app_stmt = $con->prepare("INSERT INTO mod_app_mapping (version_config_id, app_identifier) VALUES (?, ?)");
                foreach ($_POST['app_identifiers'] as $app_id) {
                    $app_id = mysqli_real_escape_string($con, $app_id);
                    $app_stmt->bind_param("is", $id, $app_id);
                    $app_stmt->execute();
                }
                $app_stmt->close();
            }

            // Xử lý tải lên file MOD nếu có
            if (!empty($lib_name) && isset($_FILES['mod_file']) && $_FILES['mod_file']['error'] == 0) {
                // Tạo thư mục files nếu chưa tồn tại
                if (!file_exists("files")) {
                    mkdir("files", 0755, true);
                }

                $target_dir = "files/";
                $target_file = $target_dir . $lib_name;

                // Nếu lib_name thay đổi và file cũ tồn tại, di chuyển file
                if ($old_lib_name != $lib_name && !empty($old_lib_name) && !empty($old_file_path) && file_exists($old_file_path)) {
                    rename($old_file_path, $target_file);
                    $file_path = "files/" . $lib_name;

                    // Cập nhật đường dẫn file
                    $update_sql = "UPDATE version_config SET file_path = '$file_path' WHERE id = $id";
                    $con->query($update_sql);

                    $success_message .= " File MOD đã được đổi tên.";
                }
                // Nếu có file mới được tải lên
                else if (move_uploaded_file($_FILES['mod_file']['tmp_name'], $target_file)) {
                    $file_path = "files/" . $lib_name;
                    chmod($target_file, 0644); // Đặt quyền cho file

                    // Cập nhật trạng thái thành ready và đường dẫn file
                    $update_sql = "UPDATE version_config SET status = 'ready', file_path = '$file_path' WHERE id = $id";
                    $con->query($update_sql);

                    $success_message .= " Và tải lên file MOD thành công!";
                } else {
                    $error_message = "Cập nhật phiên bản thành công nhưng lỗi khi tải file lên!";
                }
            }
        } else {
            $error_message = "Lỗi: " . $con->error;
        }
    }
}

// Xử lý xóa phiên bản
if (isset($_GET['delete_id'])) {
    $id = mysqli_real_escape_string($con, $_GET['delete_id']);

    // Kiểm tra xem có file MOD liên kết không
    $check_sql = "SELECT file_path FROM version_config WHERE id = $id";
    $check_result = $con->query($check_sql);

    if ($check_result->num_rows > 0) {
        $row = $check_result->fetch_assoc();
        $file_path = $row['file_path'];

        // Nếu có file, xóa file đó
        if (!empty($file_path) && file_exists($file_path)) {
            unlink($file_path);
        }
    }

    $sql = "DELETE FROM version_config WHERE id = $id";

    if ($con->query($sql)) {
        $success_message = "Xóa phiên bản thành công!";
    } else {
        $error_message = "Lỗi: " . $con->error;
    }
}

// Lấy phiên bản để chỉnh sửa
$edit_data = null;
$selected_app_ids = [];
if (isset($_GET['edit_id'])) {
    $id = mysqli_real_escape_string($con, $_GET['edit_id']);

    $sql = "SELECT * FROM version_config WHERE id = $id";
    $result = $con->query($sql);

    if ($result->num_rows > 0) {
        $edit_data = $result->fetch_assoc();

        // Lấy danh sách app_identifier đã được chọn cho MOD này
        $app_sql = "SELECT app_identifier FROM mod_app_mapping WHERE version_config_id = $id";
        $app_result = $con->query($app_sql);
        while ($app_row = $app_result->fetch_assoc()) {
            $selected_app_ids[] = $app_row['app_identifier'];
        }
    }
}

// Lấy danh sách tất cả phiên bản
$sql = "SELECT * FROM version_config ORDER BY game_code, version DESC";
$all_versions = $con->query($sql);

// Lấy danh sách game_code để lọc
$game_codes = array();
if ($tableExists) {
    $sql = "SELECT DISTINCT game_code FROM version_config ORDER BY game_code";
    $result = $con->query($sql);
    while ($row = $result->fetch_assoc()) {
        $game_codes[] = $row['game_code'];
    }
}

// Lấy danh sách app_identifier đã có
$app_identifiers = array();
$app_sql = "SELECT DISTINCT app_identifier FROM mod_app_mapping ORDER BY app_identifier";
$app_result = $con->query($app_sql);
if ($app_result) {
    while ($app_row = $app_result->fetch_assoc()) {
        $app_identifiers[] = $app_row['app_identifier'];
    }
}

// Xử lý lọc theo game_code
$filter_game = isset($_GET['filter_game']) ? $_GET['filter_game'] : '';
if (!empty($filter_game)) {
    $filter_game = mysqli_real_escape_string($con, $filter_game);
    $sql = "SELECT * FROM version_config WHERE game_code = '$filter_game' ORDER BY version DESC";
    $all_versions = $con->query($sql);
}

// Xử lý tìm kiếm
$search_term = isset($_GET['search']) ? $_GET['search'] : '';
if (!empty($search_term)) {
    $search_term = mysqli_real_escape_string($con, $search_term);
    $sql = "SELECT * FROM version_config
            WHERE version LIKE '%$search_term%'
            OR game_code LIKE '%$search_term%'
            OR apk_md5 LIKE '%$search_term%'
            OR signature LIKE '%$search_term%'
            OR lib_name LIKE '%$search_term%'
            OR title LIKE '%$search_term%'
            OR description LIKE '%$search_term%'
            ORDER BY game_code, version DESC";
    $all_versions = $con->query($sql);
}

// Xử lý lọc theo trạng thái MOD
$filter_mod_status = isset($_GET['filter_mod_status']) ? $_GET['filter_mod_status'] : '';
if (!empty($filter_mod_status)) {
    $filter_mod_status = mysqli_real_escape_string($con, $filter_mod_status);
    $sql = "SELECT * FROM version_config WHERE status = '$filter_mod_status' ORDER BY game_code, version DESC";
    $all_versions = $con->query($sql);
}
?>

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quản lý phiên bản - H-MOD</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #3f51b5;
            --secondary-color: #536dfe;
            --success-color: #4caf50;
            --warning-color: #ff9800;
            --danger-color: #f44336;
            --info-color: #03a9f4;
            --dark-color: #212121;
            --light-color: #f5f5f5;
            --border-radius: 8px;
            --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: #f8f9fa;
            color: #333;
            overflow-x: hidden;
        }

        h1, h2, h3, h4, h5, h6 {
            font-family: 'Montserrat', sans-serif;
            font-weight: 600;
        }

        /* Header */
        .header {
            background-color: white;
            box-shadow: var(--box-shadow);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            height: 64px;
            display: flex;
            align-items: center;
            padding: 0 1rem;
            transition: var(--transition);
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }

        .logo {
            display: flex;
            align-items: center;
            text-decoration: none;
        }

        .logo-text {
            font-family: 'Montserrat', sans-serif;
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary-color);
            margin-left: 0.5rem;
        }

        .user-profile {
            display: flex;
            align-items: center;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 500;
            margin-right: 0.5rem;
        }

        .user-name {
            font-weight: 500;
            margin-right: 1rem;
            display: none;
        }

        @media (min-width: 768px) {
            .user-name {
                display: block;
            }
        }

        /* Sidebar */
        .sidebar {
            position: fixed;
            left: -280px;
            top: 64px;
            height: calc(100vh - 64px);
            width: 280px;
            background-color: white;
            box-shadow: var(--box-shadow);
            z-index: 999;
            overflow-y: auto;
            transition: var(--transition);
            padding: 1rem 0;
        }

        .sidebar.active {
            left: 0;
        }

        @media (min-width: 992px) {
            .sidebar {
                left: 0;
            }

            .main-content {
                margin-left: 280px;
            }

            .sidebar-toggle {
                display: none;
            }
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu-item {
            padding: 0.75rem 1.5rem;
            display: flex;
            align-items: center;
            color: #666;
            text-decoration: none;
            transition: var(--transition);
            font-weight: 500;
        }

        .sidebar-menu-item:hover {
            background-color: rgba(63, 81, 181, 0.05);
            color: var(--primary-color);
        }

        .sidebar-menu-item.active {
            background-color: rgba(63, 81, 181, 0.1);
            color: var(--primary-color);
            border-left: 4px solid var(--primary-color);
        }

        .sidebar-menu-item i {
            margin-right: 1rem;
            font-size: 1.25rem;
        }

        .sidebar-heading {
            text-transform: uppercase;
            font-size: 0.75rem;
            font-weight: 700;
            letter-spacing: 1px;
            color: #999;
            padding: 1.5rem 1.5rem 0.5rem;
        }

        /* Main Content */
        .main-content {
            padding: 2rem 1rem 1rem;
            margin-top: 64px;
            transition: var(--transition);
        }

        @media (min-width: 768px) {
            .main-content {
                padding: 2rem;
            }
        }

        /* Page Title */
        .page-title {
            margin-bottom: 1.5rem;
        }

        .page-title h1 {
            font-size: 1.75rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .breadcrumb {
            font-size: 0.875rem;
        }

        .breadcrumb-item a {
            color: var(--primary-color);
            text-decoration: none;
        }

        /* Cards */
        .card {
            background-color: white;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-bottom: 1.5rem;
            border: none;
            overflow: hidden;
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0,0,0,0.08);
            padding: 1.25rem 1.5rem;
        }

        .card-header.primary {
            background-color: var(--primary-color);
            color: white;
        }

        .card-title {
            font-weight: 600;
            margin-bottom: 0;
            font-size: 1.125rem;
        }

        .card-body {
            padding: 1.5rem;
        }

        /* Buttons */
        .btn {
            border-radius: var(--border-radius);
            font-weight: 500;
            padding: 0.5rem 1rem;
            transition: var(--transition);
        }

        .btn-icon {
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-icon i {
            margin-right: 0.5rem;
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover, .btn-primary:focus {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-success {
            background-color: var(--success-color);
            border-color: var(--success-color);
        }

        .btn-warning {
            background-color: var(--warning-color);
            border-color: var(--warning-color);
        }

        .btn-danger {
            background-color: var(--danger-color);
            border-color: var(--danger-color);
        }

        /* Forms */
        .form-control {
            border-radius: var(--border-radius);
            padding: 0.5rem 1rem;
            border: 1px solid rgba(0,0,0,0.15);
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(63, 81, 181, 0.25);
        }

        .form-label {
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .text-hint {
            font-size: 0.875rem;
            color: #777;
            margin-top: 0.25rem;
        }

        /* Tables */
        .table {
            border-collapse: separate;
            border-spacing: 0;
        }

        .table th {
            font-weight: 600;
            background-color: rgba(0,0,0,0.02);
            border-bottom: 2px solid rgba(0,0,0,0.05);
        }

        .table th, .table td {
            padding: 1rem;
            vertical-align: middle;
        }

        .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(0,0,0,0.01);
        }

        .table-hover tbody tr:hover {
            background-color: rgba(63, 81, 181, 0.03);
        }

        /* Status badges */
        .badge {
            font-weight: 500;
            padding: 0.4em 0.8em;
            border-radius: 4px;
        }

        .badge-success {
            background-color: rgba(76, 175, 80, 0.1);
            color: var(--success-color);
        }

        .badge-danger {
            background-color: rgba(244, 67, 54, 0.1);
            color: var(--danger-color);
        }

        .badge-warning {
            background-color: rgba(255, 149, 0, 0.1);
            color: var(--warning-color);
        }

        .badge-info {
            background-color: rgba(3, 169, 244, 0.1);
            color: var(--info-color);
        }

        /* Truncated text */
        .text-truncate-container {
            display: flex;
            align-items: center;
        }

        .text-truncate-content {
            max-width: 150px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        @media (min-width: 768px) {
            .text-truncate-content {
                max-width: 250px;
            }
        }

        @media (min-width: 992px) {
            .text-truncate-content {
                max-width: 300px;
            }
        }

        /* Tooltips */
        .tooltip-icon {
            cursor: pointer;
            color: #999;
            margin-left: 0.5rem;
            font-size: 1rem;
        }

        /* Copy button */
        .copy-btn {
            cursor: pointer;
            background: none;
            border: none;
            color: #777;
            padding: 0.25rem;
            margin-left: 0.5rem;
            transition: var(--transition);
        }

        .copy-btn:hover {
            color: var(--primary-color);
        }

        /* Action buttons */
        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .action-btn {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            color: white;
            border: none;
            cursor: pointer;
            transition: var(--transition);
        }

        .action-btn-edit {
            background-color: var(--warning-color);
        }

        .action-btn-edit:hover {
            background-color: #e68a00;
        }

        .action-btn-delete {
            background-color: var(--danger-color);
        }

        .action-btn-delete:hover {
            background-color: #d32f2f;
        }

        .action-btn-upload {
            background-color: var(--info-color);
        }

        .action-btn-upload:hover {
            background-color: #0288d1;
        }

        /* Alerts */
        .alert {
            border-radius: var(--border-radius);
            border: none;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
        }

        .alert-success {
            background-color: rgba(76, 175, 80, 0.1);
            color: var(--success-color);
        }

        .alert-danger {
            background-color: rgba(244, 67, 54, 0.1);
            color: var(--danger-color);
        }

        /* Footer */
        .footer {
            text-align: center;
            padding: 1.5rem 0;
            color: #777;
            font-size: 0.875rem;
            margin-top: 2rem;
        }

        /* Overlay for mobile nav */
        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 998;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
        }

        .overlay.active {
            opacity: 1;
            visibility: visible;
        }

        /* Floating action button */
        .floating-btn {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background-color: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            cursor: pointer;
            transition: var(--transition);
            z-index: 99;
            display: none;
        }

        @media (max-width: 991px) {
            .floating-btn {
                display: flex;
            }
        }

        .floating-btn:hover {
            background-color: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0,0,0,0.3);
        }

        .floating-btn i {
            font-size: 1.5rem;
        }

        /* Modal styles */
        .modal-content {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: var(--box-shadow);
        }

        .modal-header {
            border-bottom: 1px solid rgba(0,0,0,0.08);
            padding: 1.25rem 1.5rem;
        }

        .modal-title {
            font-weight: 600;
        }

        .modal-body {
            padding: 1.5rem;
        }

        .modal-footer {
            border-top: 1px solid rgba(0,0,0,0.08);
            padding: 1rem 1.5rem;
        }

        /* Form collapsible on mobile */
        .form-collapsible {
            margin-bottom: 1.5rem;
        }

        .form-collapsible-header {
            background-color: var(--primary-color);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
        }

        .form-collapsible-content {
            background-color: white;
            border-radius: 0 0 var(--border-radius) var(--border-radius);
            box-shadow: var(--box-shadow);
            padding: 1.5rem;
            display: none;
        }

        .form-collapsible.active .form-collapsible-content {
            display: block;
        }

        /* Search and filter section */
        .filter-section {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        @media (min-width: 768px) {
            .filter-section {
                flex-direction: row;
                flex-wrap: wrap;
            }

            .search-container {
                flex-grow: 1;
                margin-right: 1rem;
            }
        }

        .search-container {
            position: relative;
        }

        .search-container .form-control {
            padding-right: 3rem;
        }

        .search-btn {
            position: absolute;
            right: 0;
            top: 0;
            height: 100%;
            width: 3rem;
            background: none;
            border: none;
            color: #777;
        }

        .search-btn:hover {
            color: var(--primary-color);
        }

        /* Back to top button */
        .back-to-top {
            position: fixed;
            bottom: 1.5rem;
            right: 1.5rem;
            width: 40px;
            height: 40px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition);
            z-index: 99;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            cursor: pointer;
        }

        .back-to-top.active {
            opacity: 1;
            visibility: visible;
        }

        .back-to-top:hover {
            background-color: var(--secondary-color);
        }

        /* Mobile optimizations */
        @media (max-width: 767px) {
            .header {
                padding: 0 0.75rem;
            }

            .card-body {
                padding: 1rem;
            }

            .action-buttons {
                flex-direction: column;
                gap: 0.5rem;
            }

            .table th, .table td {
                padding: 0.75rem;
            }

            .form-row {
                margin-bottom: 1rem;
            }

            .back-to-top {
                bottom: 5rem;
                right: 1rem;
            }
        }

        /* Tabs styling */
        .nav-tabs {
            border-bottom: 2px solid rgba(0,0,0,0.08);
        }

        .nav-tabs .nav-link {
            border: none;
            color: #777;
            font-weight: 500;
            padding: 0.75rem 1rem;
            margin-right: 0.5rem;
            transition: var(--transition);
        }

        .nav-tabs .nav-link:hover {
            color: var(--primary-color);
            border-color: transparent;
        }

        .nav-tabs .nav-link.active {
            color: var(--primary-color);
            background-color: transparent;
            border-bottom: 2px solid var(--primary-color);
            margin-bottom: -2px;
        }

        /* File upload styles */
        .upload-area {
            border: 2px dashed rgba(0,0,0,0.1);
            border-radius: var(--border-radius);
            padding: 2rem;
            text-align: center;
            transition: var(--transition);
            margin-bottom: 1.5rem;
        }

        .upload-area:hover {
            border-color: var(--primary-color);
        }

        .upload-icon {
            font-size: 2.5rem;
            color: #999;
            margin-bottom: 1rem;
        }

        .upload-text {
            margin-bottom: 1rem;
        }

        .custom-file-input::-webkit-file-upload-button {
            visibility: hidden;
        }

        .custom-file-input::before {
            content: 'Chọn file';
            display: inline-block;
            background: var(--info-color);
            color: white;
            border-radius: var(--border-radius);
            padding: 0.5rem 1rem;
            outline: none;
            white-space: nowrap;
            cursor: pointer;
            font-weight: 500;
        }

        .custom-file-input:hover::before {
            background: #0288d1;
        }

        .custom-file-input:active::before {
            background: #0277bd;
        }

        /* MOD card styles */
        .mod-card {
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            overflow: hidden;
            transition: var(--transition);
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .mod-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .mod-card-header {
            position: relative;
            background-color: var(--primary-color);
            color: white;
            padding: 1.5rem;
            text-align: center;
        }

        .mod-card-icon {
            font-size: 3rem;
            margin-bottom: 0.75rem;
        }

        .mod-card-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .mod-card-version {
            font-size: 0.875rem;
            opacity: 0.8;
        }

        .mod-card-body {
            padding: 1.5rem;
            background-color: white;
            flex-grow: 1;
        }

        .mod-card-description {
            margin-bottom: 1.25rem;
            color: #555;
            line-height: 1.6;
        }

        .mod-card-footer {
            background-color: #f9f9f9;
            padding: 1rem 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        /* File status indicator */
        .file-status {
            display: flex;
            align-items: center;
            margin-top: 0.5rem;
        }

        .file-status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 0.5rem;
        }

        .file-status-dot.ready {
            background-color: var(--success-color);
        }

        .file-status-dot.not-ready {
            background-color: var(--danger-color);
        }

        .file-status-text {
            font-size: 0.875rem;
            color: #777;
        }
    </style>
</head>
<body>
    <!-- Overlay for mobile navigation -->
    <div class="overlay" id="overlay"></div>

    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="d-flex align-items-center">
                <button type="button" class="sidebar-toggle btn btn-link text-dark p-0 me-3" id="sidebarToggle">
                    <i class="bi bi-list fs-4"></i>
                </button>
                <a href="../main/" class="logo">
                    <i class="bi bi-shield-check fs-4 text-primary"></i>
                    <span class="logo-text">H-MOD Admin</span>
                </a>
            </div>

            <div class="user-profile dropdown">
                <a href="#" class="d-flex align-items-center text-decoration-none dropdown-toggle" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <div class="user-avatar">
                        <i class="bi bi-person"></i>
                    </div>
                    <span class="user-name"><?php echo isset($_SESSION["hovaten"]) ? $_SESSION["hovaten"] : "Admin"; ?></span>
                </a>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                    <li class="dropdown-header">
                        <h6 class="mb-0"><?php echo isset($_SESSION["hovaten"]) ? $_SESSION["hovaten"] : "Admin"; ?></h6>
                        <span class="text-muted"><?php echo isset($_SESSION["level"]) ? ucfirst($_SESSION["level"]) : "Administrator"; ?></span>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item d-flex align-items-center" href="../logout/">
                            <i class="bi bi-box-arrow-right me-2"></i>
                            <span>Đăng xuất</span>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </header>

    <!-- Sidebar -->
    <aside class="sidebar" id="sidebar">
        <ul class="sidebar-menu">
            <li>
                <a href="../main/" class="sidebar-menu-item">
                    <i class="bi bi-grid"></i>
                    <span>Trang chủ</span>
                </a>
            </li>

            <div class="sidebar-heading">Quản lý</div>

            <?php if ($_SESSION["level"] == "admin"): ?>
            <li>
                <a href="../admin/" class="sidebar-menu-item">
                    <i class="bi bi-person-gear"></i>
                    <span>Quản lý Admin</span>
                </a>
            </li>
            <?php endif; ?>

            <li>
                <a href="../table-key-admin/" class="sidebar-menu-item">
                    <i class="bi bi-key"></i>
                    <span>Quản lý KEY</span>
                </a>
            </li>

            <li>
                <a href="#" class="sidebar-menu-item active">
                    <i class="bi bi-code-slash"></i>
                    <span>Quản lý phiên bản & MOD</span>
                </a>
            </li>

            <li>
                <a href="../login-history/" class="sidebar-menu-item">
                    <i class="bi bi-clock-history"></i>
                    <span>Lịch sử đăng nhập</span>
                </a>
            </li>

            <li>
                <a href="../check-version/" class="sidebar-menu-item">
                    <i class="bi bi-check-circle"></i>
                    <span>Kiểm tra phiên bản</span>
                </a>
            </li>

            <div class="sidebar-heading">Tài khoản</div>

            <li>
                <a href="../logout/" class="sidebar-menu-item">
                    <i class="bi bi-box-arrow-right"></i>
                    <span>Đăng xuất</span>
                </a>
            </li>
        </ul>
    </aside>

    <!-- Floating Action Button (Mobile) -->
    <div class="floating-btn" id="floatingBtn">
        <i class="bi bi-plus-lg"></i>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Page Title -->
        <section class="page-title">
            <h1>Quản lý phiên bản & MOD</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="../main/">Trang chủ</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Quản lý phiên bản & MOD</li>
                </ol>
            </nav>
        </section>

        <!-- Alerts Section -->
        <?php if (isset($success_message)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i>
            <?php echo $success_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            <?php echo $error_message; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        <?php endif; ?>

        <!-- Form chuyển đổi từ JSON sang SQL -->
        <?php if (file_exists("files/mod_config.json")): ?>
        <div class="card mb-4">
            <div class="card-header primary">
                <h5 class="card-title text-white">Nhập dữ liệu MOD từ JSON</h5>
            </div>
            <div class="card-body">
                <p>Bạn có thể nhập dữ liệu MOD từ file <code>files/mod_config.json</code> vào cơ sở dữ liệu.</p>
                <form method="post">
                    <button type="submit" name="import_json" class="btn btn-primary btn-icon">
                        <i class="bi bi-file-earmark-arrow-up"></i> Nhập dữ liệu từ JSON
                    </button>
                </form>
            </div>
        </div>
        <?php endif; ?>

        <!-- Tab Navigation -->
        <ul class="nav nav-tabs mb-4" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="version-tab" data-bs-toggle="tab" data-bs-target="#version-content" type="button" role="tab" aria-controls="version-content" aria-selected="true">
                    <i class="bi bi-tag me-2"></i>Phiên bản
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="mod-tab" data-bs-toggle="tab" data-bs-target="#mod-content" type="button" role="tab" aria-controls="mod-content" aria-selected="false">
                    <i class="bi bi-puzzle me-2"></i>MOD
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content">
            <!-- Version Tab -->
            <div class="tab-pane fade show active" id="version-content" role="tabpanel" aria-labelledby="version-tab">
                <div class="row">
                    <!-- Form Section -->
                    <div class="col-12" id="formSection">
                        <!-- Mobile Form (Collapsible) -->
                        <div class="d-block d-lg-none">
                            <div class="form-collapsible <?php echo ($edit_data || isset($error_message)) ? 'active' : ''; ?>" id="formCollapsible">
                                <div class="form-collapsible-header">
                                    <h5 class="m-0">
                                        <?php echo $edit_data ? 'Cập nhật phiên bản' : 'Thêm phiên bản mới'; ?>
                                    </h5>
                                    <i class="bi bi-chevron-<?php echo ($edit_data || isset($error_message)) ? 'up' : 'down'; ?>"></i>
                                </div>
                                <div class="form-collapsible-content">
                                    <form method="post" enctype="multipart/form-data">
                                        <?php if ($edit_data): ?>
                                        <input type="hidden" name="id" value="<?php echo $edit_data['id']; ?>">
                                        <?php endif; ?>

                                        <div class="mb-3">
                                            <label for="mobile_version" class="form-label">Phiên bản</label>
                                            <input type="text" class="form-control" id="mobile_version" name="version"
                                                value="<?php echo $edit_data ? htmlspecialchars($edit_data['version']) : ''; ?>" required>
                                            <div class="text-hint">Nhập MOD_VERSION (ví dụ: ********-04052025)</div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="mobile_game_code" class="form-label">Game Code</label>
                                            <input type="text" class="form-control" id="mobile_game_code" name="game_code"
                                                value="<?php echo $edit_data ? htmlspecialchars($edit_data['game_code']) : ''; ?>" required>
                                            <div class="text-hint">Mã game (ví dụ: aovvn, lqm)</div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="mobile_apk_md5" class="form-label">MD5 của APK</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="mobile_apk_md5" name="apk_md5"
                                                    value="<?php echo $edit_data ? htmlspecialchars($edit_data['apk_md5']) : ''; ?>" required>
                                                <button type="button" class="btn btn-outline-secondary copy-btn" data-clipboard-target="#mobile_apk_md5">
                                                    <i class="bi bi-clipboard"></i>
                                                </button>
                                            </div>
                                            <div class="text-hint">MD5 hash của file base.apk</div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="mobile_signature" class="form-label">Chữ ký</label>
                                            <div class="input-group">
                                            <input type="text" class="form-control" id="mobile_signature" name="signature"
                                                    value="<?php echo $edit_data ? htmlspecialchars($edit_data['signature']) : ''; ?>" required>
                                                <button type="button" class="btn btn-outline-secondary copy-btn" data-clipboard-target="#mobile_signature">
                                                    <i class="bi bi-clipboard"></i>
                                                </button>
                                            </div>
                                            <div class="text-hint">Chữ ký ứng dụng (format XX:XX:XX:XX)</div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="mobile_description" class="form-label">Mô tả</label>
                                            <textarea class="form-control" id="mobile_description" name="description" rows="3"><?php echo $edit_data ? htmlspecialchars($edit_data['description']) : ''; ?></textarea>
                                        </div>

                                        <!-- MOD Settings -->
                                        <div class="mb-3">
                                            <label for="mobile_lib_name" class="form-label">Tên File MOD</label>
                                            <input type="text" class="form-control" id="mobile_lib_name" name="lib_name"
                                                value="<?php echo $edit_data ? htmlspecialchars($edit_data['lib_name']) : ''; ?>">
                                            <div class="text-hint">Tên file .so (ví dụ: libPremium1304.so)</div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="mobile_title" class="form-label">Tiêu đề MOD</label>
                                            <input type="text" class="form-control" id="mobile_title" name="title"
                                                value="<?php echo $edit_data ? htmlspecialchars($edit_data['title']) : ''; ?>">
                                            <div class="text-hint">Tiêu đề hiển thị cho MOD</div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="mobile_safety" class="form-label">Trạng thái</label>
                                            <select class="form-select" id="mobile_safety" name="safety">
                                                <option value="safe" <?php echo ($edit_data && $edit_data['safety'] == 'safe') ? 'selected' : ''; ?>>An toàn</option>
                                                <option value="unsafe" <?php echo ($edit_data && $edit_data['safety'] == 'unsafe') ? 'selected' : ''; ?>>Cần chú ý</option>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label for="mobile_safety_message" class="form-label">Thông báo Lưu Ý</label>
                                            <textarea class="form-control" id="mobile_safety_message" name="safety_message" rows="2"><?php echo $edit_data ? htmlspecialchars($edit_data['safety_message']) : ''; ?></textarea>
                                        </div>

                                        <div class="mb-3">
                                            <label for="mobile_icon" class="form-label">Biểu tượng (Icon)</label>
                                            <input type="text" class="form-control" id="mobile_icon" name="icon"
                                                value="<?php echo $edit_data ? htmlspecialchars($edit_data['icon']) : ''; ?>">
                                            <div class="text-hint">Biểu tượng hiển thị (có thể dùng emoji)</div>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">Hiển thị cho các ứng dụng</label>
                                            <div class="app-identifiers-container">
                                                <?php
                                                // Hiển thị các app_identifier đã có
                                                foreach ($app_identifiers as $app_id) {
                                                    $checked = in_array($app_id, $selected_app_ids) ? 'checked' : '';
                                                    echo '<div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="app_identifiers[]" value="' . htmlspecialchars($app_id) . '" id="mobile_app_' . htmlspecialchars($app_id) . '" ' . $checked . '>
                                                            <label class="form-check-label" for="mobile_app_' . htmlspecialchars($app_id) . '">' . htmlspecialchars($app_id) . '</label>
                                                          </div>';
                                                }
                                                ?>

                                                <!-- Thêm app_identifier mới -->
                                                <div class="mt-2">
                                                    <div class="input-group">
                                                        <input type="text" class="form-control" id="mobile_new_app_identifier" placeholder="Thêm app_identifier mới">
                                                        <button type="button" class="btn btn-outline-secondary" id="mobile_add_app_identifier">Thêm</button>
                                                    </div>
                                                    <small class="text-muted">Nhập app_identifier mới và nhấn Thêm</small>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Thay thế phần select status trong form mobile -->
<div class="mb-3">
    <label class="form-label">Trạng thái MOD</label>
    <div class="d-flex align-items-center">
        <div class="file-status">
            <div class="file-status-dot <?php echo $edit_data ? $edit_data['status'] : 'not_ready'; ?>"></div>
            <span class="file-status-text fw-bold">
                <?php
                if ($edit_data && $edit_data['status'] == 'ready') {
                    echo '<span class="text-success">Sẵn sàng</span>';
                } else {
                    echo '<span class="text-danger">Chưa sẵn sàng</span> (tải lên file MOD để cập nhật trạng thái)';
                }
                ?>
            </span>
        </div>
        <input type="hidden" name="status" value="<?php echo $edit_data ? $edit_data['status'] : 'not_ready'; ?>">
    </div>
</div>

                                        <div class="mb-3">
                                            <label for="mobile_mod_file" class="form-label">File MOD</label>
                                            <input type="file" class="form-control" id="mobile_mod_file" name="mod_file">
                                            <div class="text-hint">Tải lên file .so cho MOD</div>
                                        </div>

                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="mobile_is_active" name="is_active"
                                                <?php echo (!$edit_data || $edit_data['is_active']) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="mobile_is_active">
                                                Kích hoạt
                                            </label>
                                        </div>

                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="mobile_vpn_check_enabled" name="vpn_check_enabled"
                                                <?php echo (!$edit_data || $edit_data['vpn_check_enabled']) ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="mobile_vpn_check_enabled">
                                                <i class="bi bi-shield-check text-primary me-1"></i>
                                                Bật kiểm tra VPN
                                            </label>
                                            <div class="text-hint">Khi bật, app sẽ kiểm tra và chặn VPN cho mod này</div>
                                        </div>

                                        <div class="d-grid gap-2">
                                            <?php if ($edit_data): ?>
                                            <button type="submit" name="update_version" class="btn btn-success btn-icon">
                                                <i class="bi bi-save"></i> Cập nhật phiên bản
                                            </button>
                                            <a href="?" class="btn btn-secondary btn-icon">
                                                <i class="bi bi-x-circle"></i> Hủy
                                            </a>
                                            <?php else: ?>
                                            <button type="submit" name="add_version" class="btn btn-primary btn-icon">
                                                <i class="bi bi-plus-circle"></i> Thêm phiên bản
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Desktop Form -->
                        <div class="card d-none d-lg-block">
                            <div class="card-header primary">
                                <h5 class="card-title text-white">
                                    <?php echo $edit_data ? 'Cập nhật phiên bản' : 'Thêm phiên bản mới'; ?>
                                </h5>
                            </div>
                            <div class="card-body">
                                <form method="post" enctype="multipart/form-data">
                                    <?php if ($edit_data): ?>
                                    <input type="hidden" name="id" value="<?php echo $edit_data['id']; ?>">
                                    <?php endif; ?>

                                    <div class="row mb-3">
                                        <label for="desktop_version" class="col-sm-2 col-form-label">Phiên bản</label>
                                        <div class="col-sm-10">
                                            <input type="text" class="form-control" id="desktop_version" name="version"
                                                value="<?php echo $edit_data ? htmlspecialchars($edit_data['version']) : ''; ?>" required>
                                            <div class="text-hint">Nhập MOD_VERSION (ví dụ: ********-04052025)</div>
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <label for="desktop_game_code" class="col-sm-2 col-form-label">Game Code</label>
                                        <div class="col-sm-10">
                                            <input type="text" class="form-control" id="desktop_game_code" name="game_code"
                                                value="<?php echo $edit_data ? htmlspecialchars($edit_data['game_code']) : ''; ?>" required>
                                            <div class="text-hint">Mã game (ví dụ: aovvn, lqm)</div>
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <label for="desktop_apk_md5" class="col-sm-2 col-form-label">MD5 của APK</label>
                                        <div class="col-sm-10">
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="desktop_apk_md5" name="apk_md5"
                                                    value="<?php echo $edit_data ? htmlspecialchars($edit_data['apk_md5']) : ''; ?>" required>
                                                <button type="button" class="btn btn-outline-secondary copy-btn" data-clipboard-target="#desktop_apk_md5">
                                                    <i class="bi bi-clipboard"></i>
                                                </button>
                                            </div>
                                            <div class="text-hint">MD5 hash của file base.apk</div>
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <label for="desktop_signature" class="col-sm-2 col-form-label">Chữ ký</label>
                                        <div class="col-sm-10">
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="desktop_signature" name="signature"
                                                    value="<?php echo $edit_data ? htmlspecialchars($edit_data['signature']) : ''; ?>" required>
                                                <button type="button" class="btn btn-outline-secondary copy-btn" data-clipboard-target="#desktop_signature">
                                                    <i class="bi bi-clipboard"></i>
                                                </button>
                                            </div>
                                            <div class="text-hint">Chữ ký ứng dụng (format XX:XX:XX:XX)</div>
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <label for="desktop_description" class="col-sm-2 col-form-label">Mô tả</label>
                                        <div class="col-sm-10">
                                            <textarea class="form-control" id="desktop_description" name="description" rows="3"><?php echo $edit_data ? htmlspecialchars($edit_data['description']) : ''; ?></textarea>
                                        </div>
                                    </div>

                                    <!-- MOD Settings -->
                                    <hr class="my-4">
                                    <h6 class="mb-3 text-primary">Thiết lập MOD</h6>

                                    <div class="row mb-3">
                                        <label for="desktop_lib_name" class="col-sm-2 col-form-label">Tên File MOD</label>
                                        <div class="col-sm-10">
                                            <input type="text" class="form-control" id="desktop_lib_name" name="lib_name"
                                                value="<?php echo $edit_data ? htmlspecialchars($edit_data['lib_name']) : ''; ?>">
                                            <div class="text-hint">Tên file .so (ví dụ: libPremium1304.so)</div>
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <label for="desktop_title" class="col-sm-2 col-form-label">Tiêu đề MOD</label>
                                        <div class="col-sm-10">
                                            <input type="text" class="form-control" id="desktop_title" name="title"
                                                value="<?php echo $edit_data ? htmlspecialchars($edit_data['title']) : ''; ?>">
                                            <div class="text-hint">Tiêu đề hiển thị cho MOD</div>
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <label for="desktop_safety" class="col-sm-2 col-form-label">Trạng thái</label>
                                        <div class="col-sm-10">
                                            <select class="form-select" id="desktop_safety" name="safety">
                                                <option value="safe" <?php echo ($edit_data && $edit_data['safety'] == 'safe') ? 'selected' : ''; ?>>An toàn</option>
                                                <option value="unsafe" <?php echo ($edit_data && $edit_data['safety'] == 'unsafe') ? 'selected' : ''; ?>>Cần chú ý</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <label for="desktop_safety_message" class="col-sm-2 col-form-label">Thông báo Lưu Ý</label>
                                        <div class="col-sm-10">
                                            <textarea class="form-control" id="desktop_safety_message" name="safety_message" rows="2"><?php echo $edit_data ? htmlspecialchars($edit_data['safety_message']) : ''; ?></textarea>
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <label for="desktop_icon" class="col-sm-2 col-form-label">Biểu tượng (Icon)</label>
                                        <div class="col-sm-10">
                                            <input type="text" class="form-control" id="desktop_icon" name="icon"
                                                value="<?php echo $edit_data ? htmlspecialchars($edit_data['icon']) : ''; ?>">
                                            <div class="text-hint">Biểu tượng hiển thị (có thể dùng emoji)</div>
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <label class="col-sm-2 col-form-label">Hiển thị cho các ứng dụng</label>
                                        <div class="col-sm-10">
                                            <div class="app-identifiers-container">
                                                <?php
                                                // Hiển thị các app_identifier đã có
                                                foreach ($app_identifiers as $app_id) {
                                                    $checked = in_array($app_id, $selected_app_ids) ? 'checked' : '';
                                                    echo '<div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="app_identifiers[]" value="' . htmlspecialchars($app_id) . '" id="desktop_app_' . htmlspecialchars($app_id) . '" ' . $checked . '>
                                                            <label class="form-check-label" for="desktop_app_' . htmlspecialchars($app_id) . '">' . htmlspecialchars($app_id) . '</label>
                                                          </div>';
                                                }
                                                ?>

                                                <!-- Thêm app_identifier mới -->
                                                <div class="mt-2">
                                                    <div class="input-group">
                                                        <input type="text" class="form-control" id="desktop_new_app_identifier" placeholder="Thêm app_identifier mới">
                                                        <button type="button" class="btn btn-outline-secondary" id="desktop_add_app_identifier">Thêm</button>
                                                    </div>
                                                    <small class="text-muted">Nhập app_identifier mới và nhấn Thêm</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Thay thế phần select status trong form desktop -->
<div class="row mb-3">
    <label class="col-sm-2 col-form-label">Trạng thái MOD</label>
    <div class="col-sm-10">
        <div class="d-flex align-items-center">
            <div class="file-status">
                <div class="file-status-dot <?php echo $edit_data ? $edit_data['status'] : 'not_ready'; ?>"></div>
                <span class="file-status-text fw-bold">
                    <?php
                    if ($edit_data && $edit_data['status'] == 'ready') {
                        echo '<span class="text-success">Sẵn sàng</span>';
                    } else {
                        echo '<span class="text-danger">Chưa sẵn sàng</span> <small class="text-muted">(tải lên file MOD để cập nhật trạng thái)</small>';
                    }
                    ?>
                </span>
            </div>
            <input type="hidden" name="status" value="<?php echo $edit_data ? $edit_data['status'] : 'not_ready'; ?>">
        </div>
    </div>
</div>

                                    <div class="row mb-3">
                                        <label for="desktop_mod_file" class="col-sm-2 col-form-label">File MOD</label>
                                        <div class="col-sm-10">
                                            <input type="file" class="form-control" id="desktop_mod_file" name="mod_file">
                                            <div class="text-hint">Tải lên file .so cho MOD</div>
                                        </div>
                                    </div>

                                    <div class="row mb-3">
                                        <div class="col-sm-10 offset-sm-2">
                                            <div class="form-check mb-2">
                                                <input class="form-check-input" type="checkbox" id="desktop_is_active" name="is_active"
                                                    <?php echo (!$edit_data || $edit_data['is_active']) ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="desktop_is_active">
                                                    Kích hoạt
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="desktop_vpn_check_enabled" name="vpn_check_enabled"
                                                    <?php echo (!$edit_data || $edit_data['vpn_check_enabled']) ? 'checked' : ''; ?>>
                                                <label class="form-check-label" for="desktop_vpn_check_enabled">
                                                    <i class="bi bi-shield-check text-primary me-1"></i>
                                                    Bật kiểm tra VPN
                                                </label>
                                                <div class="text-hint">Khi bật, app sẽ kiểm tra và chặn VPN cho mod này</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-sm-10 offset-sm-2">
                                            <?php if ($edit_data): ?>
                                            <button type="submit" name="update_version" class="btn btn-success btn-icon">
                                                <i class="bi bi-save"></i> Cập nhật phiên bản
                                            </button>
                                            <a href="?" class="btn btn-secondary btn-icon ms-2">
                                                <i class="bi bi-x-circle"></i> Hủy
                                            </a>
                                            <?php else: ?>
                                            <button type="submit" name="add_version" class="btn btn-primary btn-icon">
                                                <i class="bi bi-plus-circle"></i> Thêm phiên bản
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <!-- List Section -->
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title">Danh sách phiên bản</h5>
                            </div>
                            <div class="card-body">
                                <!-- Search and Filter -->
                                <div class="filter-section">
                                    <div class="search-container">
                                        <form method="get" class="d-flex">
                                            <input type="text" name="search" class="form-control" placeholder="Tìm kiếm..."
                                                   value="<?php echo htmlspecialchars($search_term); ?>">
                                            <button type="submit" class="search-btn">
                                                <i class="bi bi-search"></i>
                                            </button>
                                        </form>
                                    </div>

                                    <div class="filter-container">
                                        <form method="get">
                                            <select name="filter_game" class="form-select" onchange="this.form.submit()">
                                                <option value="">Tất cả game</option>
                                                <?php foreach ($game_codes as $code): ?>
                                                <option value="<?php echo htmlspecialchars($code); ?>" <?php echo ($filter_game == $code) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($code); ?>
                                                </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </form>
                                    </div>

                                    <?php if (!empty($search_term) || !empty($filter_game)): ?>
                                    <a href="?" class="btn btn-outline-secondary">
                                        <i class="bi bi-x-circle"></i> Xóa bộ lọc
                                    </a>
                                    <?php endif; ?>
                                </div>

                                <!-- Versions Table -->
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th class="d-none d-md-table-cell">ID</th>
                                                <th>Phiên bản</th>
                                                <th>Game</th>
                                                <th>MD5</th>
                                                <th class="d-none d-lg-table-cell">Chữ ký</th>
                                                <th>MOD</th>
                                                <th>VPN Check</th>
                                                <th>Trạng thái</th>
                                                <th class="d-none d-md-table-cell">Ngày tạo</th>
                                                <th>Thao tác</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if ($all_versions && $all_versions->num_rows > 0): ?>
                                                <?php while ($row = $all_versions->fetch_assoc()): ?>
                                                <tr>
                                                    <td class="d-none d-md-table-cell"><?php echo $row['id']; ?></td>
                                                    <td><?php echo htmlspecialchars($row['version']); ?></td>
                                                    <td><?php echo htmlspecialchars($row['game_code']); ?></td>
                                                    <td>
                                                        <div class="text-truncate-container">
                                                            <span class="text-truncate-content" data-bs-toggle="tooltip"
                                                                title="<?php echo htmlspecialchars($row['apk_md5']); ?>"><?php echo htmlspecialchars($row['apk_md5']); ?></span>
                                                            <button type="button" class="copy-btn" data-clipboard-text="<?php echo htmlspecialchars($row['apk_md5']); ?>">
                                                                <i class="bi bi-clipboard"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                    <td class="d-none d-lg-table-cell">
                                                        <div class="text-truncate-container">
                                                            <span class="text-truncate-content" data-bs-toggle="tooltip"
                                                                title="<?php echo htmlspecialchars($row['signature']); ?>"><?php echo htmlspecialchars($row['signature']); ?></span>
                                                            <button type="button" class="copy-btn" data-clipboard-text="<?php echo htmlspecialchars($row['signature']); ?>">
                                                                <i class="bi bi-clipboard"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <?php if (!empty($row['lib_name'])): ?>
                                                            <span data-bs-toggle="tooltip" title="<?php echo htmlspecialchars($row['title'] ?: $row['lib_name']); ?>">
                                                                <?php echo !empty($row['icon']) ? $row['icon'] : '<i class="bi bi-joystick"></i>'; ?>
                                                            </span>
                                                            <div class="file-status">
                                                                <div class="file-status-dot <?php echo $row['status']; ?>"></div>
                                                                <span class="file-status-text">
                                                                    <?php echo $row['status'] == 'ready' ? 'Sẵn sàng' : 'Chưa sẵn sàng'; ?>
                                                                </span>
                                                            </div>
                                                        <?php else: ?>
                                                            <span class="text-muted">Không có</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php if ($row['vpn_check_enabled']): ?>
                                                        <span class="badge badge-success" data-bs-toggle="tooltip" title="VPN sẽ bị chặn cho mod này">
                                                            <i class="bi bi-shield-check"></i> Bật
                                                        </span>
                                                        <?php else: ?>
                                                        <span class="badge badge-warning" data-bs-toggle="tooltip" title="VPN được phép cho mod này">
                                                            <i class="bi bi-shield-x"></i> Tắt
                                                        </span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php if ($row['is_active']): ?>
                                                        <span class="badge badge-success">Kích hoạt</span>
                                                        <?php else: ?>
                                                        <span class="badge badge-danger">Vô hiệu</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td class="d-none d-md-table-cell"><?php echo $row['created_at']; ?></td>
                                                    <td>
                                                        <div class="action-buttons">
                                                            <a href="?edit_id=<?php echo $row['id']; ?>" class="action-btn action-btn-edit" data-bs-toggle="tooltip" title="Chỉnh sửa">
                                                                <i class="bi bi-pencil"></i>
                                                            </a>
                                                            <button type="button" class="action-btn action-btn-delete" onclick="confirmDelete(<?php echo $row['id']; ?>)" data-bs-toggle="tooltip" title="Xóa">
                                                                <i class="bi bi-trash"></i>
                                                            </button>
                                                            <?php if (!empty($row['lib_name']) && $row['status'] != 'ready'): ?>
                                                            <button type="button" class="action-btn action-btn-upload" onclick="showUploadModal(<?php echo $row['id']; ?>, '<?php echo htmlspecialchars($row['lib_name']); ?>')" data-bs-toggle="tooltip" title="Tải lên MOD">
                                                                <i class="bi bi-upload"></i>
                                                            </button>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                </tr>
                                                <?php endwhile; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="10" class="text-center py-4">
                                                        <i class="bi bi-info-circle me-2"></i> Không có phiên bản nào được tìm thấy
                                                    </td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- MOD Tab -->
            <div class="tab-pane fade" id="mod-content" role="tabpanel" aria-labelledby="mod-tab">
                <div class="row">
                    <!-- Filter Section -->
                    <div class="col-12 mb-4">
                        <div class="filter-section">
                            <div class="search-container">
                                <form method="get" class="d-flex">
                                    <input type="hidden" name="tab" value="mod">
                                    <input type="text" name="search" class="form-control" placeholder="Tìm kiếm MOD..."
                                           value="<?php echo htmlspecialchars($search_term); ?>">
                                    <button type="submit" class="search-btn">
                                        <i class="bi bi-search"></i>
                                    </button>
                                </form>
                            </div>

                            <div class="filter-container">
                                <form method="get">
                                    <input type="hidden" name="tab" value="mod">
                                    <select name="filter_mod_status" class="form-select" onchange="this.form.submit()">
                                        <option value="">Tất cả trạng thái</option>
                                        <option value="ready" <?php echo ($filter_mod_status == 'ready') ? 'selected' : ''; ?>>Sẵn sàng</option>
                                        <option value="not_ready" <?php echo ($filter_mod_status == 'not_ready') ? 'selected' : ''; ?>>Chưa sẵn sàng</option>
                                    </select>
                                </form>
                            </div>

                            <?php if (!empty($search_term) || !empty($filter_mod_status)): ?>
                            <a href="?tab=mod" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle"></i> Xóa bộ lọc
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- MOD Cards -->
                    <?php
                    $mod_versions = $con->query("SELECT * FROM version_config WHERE lib_name IS NOT NULL AND lib_name != '' ORDER BY status ASC, game_code ASC, version DESC");
                    if ($mod_versions && $mod_versions->num_rows > 0):
                    ?>
                    <div class="row">
                        <?php while ($mod = $mod_versions->fetch_assoc()): ?>
                        <div class="col-md-6 col-lg-4 mb-4">
                            <div class="mod-card">
                                <div class="mod-card-header" style="background-color: <?php echo $mod['safety'] == 'safe' ? 'var(--success-color)' : 'var(--warning-color)'; ?>">
                                    <div class="mod-card-icon">
                                        <?php echo !empty($mod['icon']) ? $mod['icon'] : '<i class="bi bi-joystick"></i>'; ?>
                                    </div>
                                    <h3 class="mod-card-title">
                                        <?php echo htmlspecialchars($mod['title'] ?: $mod['lib_name']); ?>
                                    </h3>
                                    <p class="mod-card-version">
                                        <?php echo htmlspecialchars($mod['version']); ?> (<?php echo htmlspecialchars($mod['game_code']); ?>)
                                    </p>
                                </div>
                                <div class="mod-card-body">
                                    <div class="mod-card-description">
                                        <?php echo nl2br(htmlspecialchars($mod['description'])); ?>
                                    </div>

                                    <?php if (!empty($mod['safety_message'])): ?>
                                    <div class="alert <?php echo $mod['safety'] == 'safe' ? 'alert-success' : 'alert-warning'; ?> p-2">
                                        <small><i class="bi <?php echo $mod['safety'] == 'safe' ? 'bi-shield-check' : 'bi-exclamation-triangle'; ?> me-1"></i> <?php echo htmlspecialchars($mod['safety_message']); ?></small>
                                    </div>
                                    <?php endif; ?>

                                    <div class="d-flex justify-content-between align-items-center mt-3">
                                        <span class="badge <?php echo $mod['safety'] == 'safe' ? 'badge-success' : 'badge-warning'; ?>">
                                            <?php echo $mod['safety'] == 'safe' ? 'An toàn' : 'Cần chú ý'; ?>
                                        </span>
                                        <span class="badge <?php echo $mod['status'] == 'ready' ? 'badge-info' : 'badge-danger'; ?>">
                                            <?php echo $mod['status'] == 'ready' ? 'Sẵn sàng' : 'Chưa sẵn sàng'; ?>
                                        </span>
                                    </div>
                                </div>

                                <div class="mod-card-footer">
                                    <small class="text-muted"><?php echo htmlspecialchars($mod['lib_name']); ?></small>

                                    <div class="action-buttons">
                                        <a href="?edit_id=<?php echo $mod['id']; ?>" class="btn btn-sm btn-warning">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <?php if ($mod['status'] != 'ready'): ?>
                                        <button type="button" class="btn btn-sm btn-info" onclick="showUploadModal(<?php echo $mod['id']; ?>, '<?php echo htmlspecialchars($mod['lib_name']); ?>')">
                                            <i class="bi bi-upload"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endwhile; ?>
                    </div>
                    <?php else: ?>
                    <div class="col-12">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i> Không có MOD nào được tìm thấy. Hãy thêm MOD bằng cách điền thông tin MOD trong tab Phiên bản.
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div>
            &copy; <?php echo date('Y'); ?> H-MOD. Đã đăng ký bản quyền.
        </div>
    </footer>

    <!-- Back to Top Button -->
    <a href="#" class="back-to-top" id="backToTop">
        <i class="bi bi-arrow-up"></i>
    </a>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">Xác nhận xóa</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Bạn có chắc chắn muốn xóa phiên bản này không?</p>
                    <p class="text-danger"><small><i class="bi bi-exclamation-triangle me-2"></i>Hành động này không thể hoàn tác.</small></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                    <a href="#" id="deleteConfirmBtn" class="btn btn-danger">Xóa</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Upload MOD Modal -->
    <div class="modal fade" id="uploadModal" tabindex="-1" aria-labelledby="uploadModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="uploadModalLabel">Tải lên file MOD</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form method="post" enctype="multipart/form-data" id="uploadForm">
                        <input type="hidden" name="mod_id" id="mod_id" value="">

                        <div class="upload-area">
                            <div class="upload-icon">
                                <i class="bi bi-cloud-arrow-up"></i>
                            </div>
                            <p class="upload-text">Tải lên file <span id="lib_name_display"></span></p>
                            <input type="file" name="mod_file" required class="form-control">
                        </div>

                        <div class="d-grid">
                            <button type="submit" name="upload_mod" class="btn btn-primary">
                                <i class="bi bi-upload me-2"></i> Tải lên
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Clipboard.js -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.8/clipboard.min.js"></script>

    <!-- Custom JS -->
    <script>
        // DOM Content Loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Sidebar Toggle
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('overlay');

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('active');
                    overlay.classList.toggle('active');
                });
            }

            if (overlay) {
                overlay.addEventListener('click', function() {
                    sidebar.classList.remove('active');
                    overlay.classList.remove('active');
                });
            }

            // Mobile Form Collapsible
            const formCollapsible = document.getElementById('formCollapsible');

            if (formCollapsible) {
                const header = formCollapsible.querySelector('.form-collapsible-header');
                const content = formCollapsible.querySelector('.form-collapsible-content');
                const icon = header.querySelector('i');

                header.addEventListener('click', function() {
                    formCollapsible.classList.toggle('active');

                    if (formCollapsible.classList.contains('active')) {
                        content.style.display = 'block';
                        icon.classList.remove('bi-chevron-down');
                        icon.classList.add('bi-chevron-up');
                    } else {
                        content.style.display = 'none';
                        icon.classList.remove('bi-chevron-up');
                        icon.classList.add('bi-chevron-down');
                    }
                });
            }

            // Floating Button (Mobile)
            const floatingBtn = document.getElementById('floatingBtn');

            if (floatingBtn) {
                floatingBtn.addEventListener('click', function() {
                    const formSection = document.getElementById('formSection');

                    if (formSection) {
                        formSection.scrollIntoView({ behavior: 'smooth' });

                        // Expand form if it's collapsed
                        if (formCollapsible && !formCollapsible.classList.contains('active')) {
                            formCollapsible.classList.add('active');
                            formCollapsible.querySelector('.form-collapsible-content').style.display = 'block';
                            const icon = formCollapsible.querySelector('.form-collapsible-header i');
                            icon.classList.remove('bi-chevron-down');
                            icon.classList.add('bi-chevron-up');
                        }
                    }
                });
            }

            // Back to Top Button
            const backToTopBtn = document.getElementById('backToTop');

            if (backToTopBtn) {
                window.addEventListener('scroll', function() {
                    if (window.scrollY > 300) {
                        backToTopBtn.classList.add('active');
                    } else {
                        backToTopBtn.classList.remove('active');
                    }
                });

                backToTopBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                });
            }

            // Initialize Clipboard.js
            const clipboard = new ClipboardJS('.copy-btn');

            clipboard.on('success', function(e) {
                const button = e.trigger;
                const originalIcon = button.innerHTML;

                button.innerHTML = '<i class="bi bi-check"></i>';
                button.style.color = 'var(--success-color)';

                setTimeout(function() {
                    button.innerHTML = originalIcon;
                    button.style.color = '';
                }, 1500);

                e.clearSelection();
            });

            // Initialize Bootstrap Tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            const tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Xử lý thêm app_identifier mới (mobile)
            const mobileAddAppIdentifierBtn = document.getElementById('mobile_add_app_identifier');
            if (mobileAddAppIdentifierBtn) {
                mobileAddAppIdentifierBtn.addEventListener('click', function() {
                    addNewAppIdentifier('mobile_new_app_identifier', 'mobile_app_');
                });
            }

            // Xử lý thêm app_identifier mới (desktop)
            const desktopAddAppIdentifierBtn = document.getElementById('desktop_add_app_identifier');
            if (desktopAddAppIdentifierBtn) {
                desktopAddAppIdentifierBtn.addEventListener('click', function() {
                    addNewAppIdentifier('desktop_new_app_identifier', 'desktop_app_');
                });
            }

            // Hàm thêm app_identifier mới
            function addNewAppIdentifier(inputId, idPrefix) {
                const input = document.getElementById(inputId);
                const value = input.value.trim();

                if (value) {
                    const container = input.closest('.app-identifiers-container');
                    const checkboxId = idPrefix + value;

                    // Kiểm tra xem app_identifier đã tồn tại chưa
                    if (!document.getElementById(checkboxId)) {
                        const div = document.createElement('div');
                        div.className = 'form-check';
                        div.innerHTML = `
                            <input class="form-check-input" type="checkbox" name="app_identifiers[]" value="${value}" id="${checkboxId}" checked>
                            <label class="form-check-label" for="${checkboxId}">${value}</label>
                        `;

                        // Thêm vào trước phần thêm mới
                        container.insertBefore(div, input.parentNode.parentNode);

                        // Xóa giá trị input
                        input.value = '';
                    } else {
                        alert('app_identifier này đã tồn tại!');
                    }
                }
            }

            // Check for active tab from URL
            const urlParams = new URLSearchParams(window.location.search);
            const tab = urlParams.get('tab');

            if (tab === 'mod') {
                const modTab = document.querySelector('#mod-tab');
                if (modTab) {
                    const tabInstance = new bootstrap.Tab(modTab);
                    tabInstance.show();
                }
            }
        });

        // Delete Confirmation
        function confirmDelete(id) {
            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            const deleteConfirmBtn = document.getElementById('deleteConfirmBtn');

            deleteConfirmBtn.href = '?delete_id=' + id;
            deleteModal.show();
        }

        // Show Upload Modal
        function showUploadModal(id, libName) {
            const uploadModal = new bootstrap.Modal(document.getElementById('uploadModal'));
            document.getElementById('mod_id').value = id;
            document.getElementById('lib_name_display').textContent = libName;
            uploadModal.show();
        }
    </script>
</body>
</html>