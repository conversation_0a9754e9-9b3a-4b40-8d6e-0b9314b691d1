#pragma once


std::string RandomString(const int len);
std::string CalcMD5(std::string s);
std::string CalcSHA256(std::string s);
std::string RandomString(const int len) {
    static const char alphanumerics[] = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
    srand((unsigned) time(0) * getpid());

    std::string tmp;
    tmp.reserve(len);
    for (int i = 0; i < len; ++i) {
        tmp += alphanumerics[rand() % (sizeof(alphanumerics) - 1)];
    }
    return tmp;
}

std::string CalcMD5(std::string s) {
    std::string result;

    unsigned char hash[MD5_DIGEST_LENGTH];
    char tmp[4];

    MD5_CTX md5;
    MD5_Init(&md5);
    MD5_Update(&md5, s.c_str(), s.length());
    MD5_Final(hash, &md5);
    for (unsigned char i : hash) {
        sprintf(tmp, "%02x", i);
        result += tmp;
    }
    return result;
}



/////---OFFSETS ONLINE---////

const char *g_key = OBFUSCATE("094412704612345689");
const char *g_iv = OBFUSCATE("0123456789012345");


string EncryptionAES(const string& strSrc)
{
    size_t length = strSrc.length();
    int block_num = length / BLOCK_SIZE + 1;
    char* szDataIn = new char[block_num * BLOCK_SIZE + 1];
    memset(szDataIn, 0x00, block_num * BLOCK_SIZE + 1);
    strcpy(szDataIn, strSrc.c_str());

    int k = length % BLOCK_SIZE;
    int j = length / BLOCK_SIZE;
    int padding = BLOCK_SIZE - k;
    for (int i = 0; i < padding; i++)
    {
        szDataIn[j * BLOCK_SIZE + k + i] = padding;
    }
    szDataIn[block_num * BLOCK_SIZE] = '\0';

    char *szDataOut = new char[block_num * BLOCK_SIZE + 1];
    memset(szDataOut, 0, block_num * BLOCK_SIZE + 1);

    AES aes;
    aes.MakeKey(g_key, g_iv, 16, 16);
    aes.Encrypt(szDataIn, szDataOut, block_num * BLOCK_SIZE, AES::CBC);
    string str = base64_encode((unsigned char*) szDataOut,
                               block_num * BLOCK_SIZE);
    delete[] szDataIn;
    delete[] szDataOut;
    return str;
}


string DecryptionAES(const string& strSrc)
{
    string strData = base64_decode(strSrc);
    size_t length = strData.length();
    char *szDataIn = new char[length + 1];
    memcpy(szDataIn, strData.c_str(), length+1);
    char *szDataOut = new char[length + 1];
    memcpy(szDataOut, strData.c_str(), length+1);

    AES aes;
    aes.MakeKey(g_key, g_iv, 16, 16);
    aes.Decrypt(szDataIn, szDataOut, length, AES::CBC);

    if (0x00 < szDataOut[length - 1] <= 0x16)
    {
        int tmp = szDataOut[length - 1];
        for (int i = length - 1; i >= length - tmp; i--)
        {
            if (szDataOut[i] != tmp)
            {
                memset(szDataOut, 0, length);
                cout << "q" << endl;
                break;
            }
            else
                szDataOut[i] = 0;
        }
    }
    string strDest(szDataOut);
    delete[] szDataIn;
    delete[] szDataOut;
    return strDest;
}

std::string offset = "";
void OffsetsOnline() {
    std::string errMsg;

    struct MemoryStruct chunk{};
    chunk.memory = (char *) malloc(1);
    chunk.size = 0;

    CURL *curl;
    CURLcode res;
    curl = curl_easy_init();
    if (curl) {
        curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "POST");
        curl_easy_setopt(curl, CURLOPT_URL, std::string(OBFUSCATE("https://hmod.io.vn/public/files/Check2.php")).c_str());
        curl_easy_setopt(curl, CURLOPT_FOLLOWLOCATION, 1L);
        curl_easy_setopt(curl, CURLOPT_DEFAULT_PROTOCOL, "https");
        struct curl_slist *headers = NULL;
        headers = curl_slist_append(headers, "Content-Type: application/x-www-form-urlencoded");
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);

        char data[4096];
        sprintf(data, "game=lqm&nhin_thay_chu_nay=%s","1 đàn chó =)) ");
        curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data);

        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteMemoryCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, (void *) &chunk);

        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYPEER, 0L);
        curl_easy_setopt(curl, CURLOPT_SSL_VERIFYHOST, 0L);

        res = curl_easy_perform(curl);
        
        if (res == CURLE_OK) {
            offset = DecryptionAES(base64_decode(std::string(chunk.memory)));
        } else {
            errMsg = curl_easy_strerror(res);
        }
    }
    curl_easy_cleanup(curl);
}


bool HackMap;
void (*_LActorRoot_Visible)(...);
void LActorRoot_Visible(void *instance, int camp, bool bVisible, const bool forceSync = false) {
    if (instance != nullptr && HackMap) {     
     bVisible = true;       
    } 
 return _LActorRoot_Visible(instance, camp, bVisible, forceSync);
}

struct {
    float GetFieldOfView = 0;
    float SetFieldOfView = 0;
    bool Active = false;
} WideView;




///  ---CAM XA ---///
float (*old_GetCameraHeightRateValue)(void *instance, int *type);
float GetCameraHeightRateValue(void *instance, int *type) {
    if (instance != NULL) {
        WideView.GetFieldOfView = old_GetCameraHeightRateValue(instance, type);
        if (WideView.SetFieldOfView != 0) {
            //WideView.Active = false;
            return (float) WideView.SetFieldOfView + WideView.GetFieldOfView;
        }
        return WideView.GetFieldOfView;
    }
    return old_GetCameraHeightRateValue(instance, type);
}

void (*OnCameraHeightChanged)(void *instance);
void (*old_CameraSystemUpdate)(void *instance);
void CameraSystemUpdate(void *instance) {
    if (instance != NULL && WideView.Active) {
        OnCameraHeightChanged(instance);
    }
    old_CameraSystemUpdate(instance);
}







void (*old_ShowSkillStateInfo)(void *instance, bool bShow);
void ShowSkillStateInfo(void *instance, bool bShow) {
    if (instance != NULL) {
      bShow = true;
    }
    old_ShowSkillStateInfo(instance, bShow);

}
  

   // Hiện Icon
bool (*old_ShowHeroInfo)(void *instance);
bool ShowHeroInfo(void *instance) {
    {
        return true;
    }
    return old_ShowHeroInfo(instance);

}

 //Hiện máu //

void (*old_ShowHeroHpInfo)(void *instance, bool bShow);
void ShowHeroHpInfo(void *instance, bool bShow) {
   // if (instance != NULL) {
       {
      bShow = true;
    } 
    old_ShowHeroHpInfo(instance, bShow);
}



 /////Lịch sử đấu////
bool (*old_get_IsHostProfile)(void *instance);
bool get_IsHostProfile(void *instance) {
  //  if (instance != NULL && LSD) 
    {
        return true;
    }
    return old_get_IsHostProfile(instance);

}





  /////FPS 60////

bool (*old_get_Supported60FPSMode)(void *instance);
bool get_Supported60FPSMode(void *instance) {
  //  if (instance != NULL && FPS60) 
    {
        return true;
    }
    return old_get_Supported60FPSMode(instance);

}






#define Field_AovTdr_dll_CSProtocol_COMDT_HERO_COMMON_INFO_dwHeroID IL2Cpp::Il2CppGetFieldOffset(OBFUSCATE("AovTdr.dll"), OBFUSCATE("CSProtocol"), OBFUSCATE("COMDT_HERO_COMMON_INFO"), OBFUSCATE("dwHeroID"))

#define Field_AovTdr_dll_CSProtocol_COMDT_HERO_COMMON_INFO_wSkinID IL2Cpp::Il2CppGetFieldOffset(OBFUSCATE("AovTdr.dll"), OBFUSCATE("CSProtocol"), OBFUSCATE("COMDT_HERO_COMMON_INFO"), OBFUSCATE("wSkinID"))
bool unlockskin = false;

enum class TdrErrorType {};


namespace CSProtocol {
    class COMDT_HERO_COMMON_INFO {
    public:
        uint32_t getdwHeroID() {
            if (this == nullptr) {return 0;}
            return *(uint32_t *)((uint64_t)this + Field_AovTdr_dll_CSProtocol_COMDT_HERO_COMMON_INFO_dwHeroID);
        };
        uint16_t getwSkinID() {
            if (this == nullptr) {return 0;}
            return *(uint16_t *)((uint64_t)this + Field_AovTdr_dll_CSProtocol_COMDT_HERO_COMMON_INFO_wSkinID);
        };
        
        void setdwHeroID(uint32_t dwHeroID) {
            if (this == nullptr) {return;}
            *(uint32_t *)((uint64_t)this + Field_AovTdr_dll_CSProtocol_COMDT_HERO_COMMON_INFO_dwHeroID) = dwHeroID;
        };
        void setwSkinID(uint16_t wSkinID) {
            if (this == nullptr) {return;}
            *(uint16_t *)((uint64_t)this + Field_AovTdr_dll_CSProtocol_COMDT_HERO_COMMON_INFO_wSkinID) = wSkinID;
        };
        //
    };
    
    struct saveData {
        static uint32_t heroId;
        static uint16_t skinId;
        static bool enable;
        static std::vector<std::pair<COMDT_HERO_COMMON_INFO*, uint16_t>> arrayUnpackSkin;
        
        static void setData(uint32_t hId, uint16_t sId) {
            heroId = hId;
            skinId = sId;
        }
        
        static void setEnable(bool eb) {
            enable = eb;
        }
        
        static uint32_t getHeroId() {
            return heroId;
        }

        static uint16_t getSkinId() {
            return skinId;
        }
        
        static bool getEnable() {
            return enable;
        }
        
        static void resetArrayUnpackSkin() {
            if (!saveData::arrayUnpackSkin.empty()) {
                for (const auto& skinInfo : saveData::arrayUnpackSkin) {
                    COMDT_HERO_COMMON_INFO* heroInfo = skinInfo.first;
                    uint16_t skinId = skinInfo.second;
            
                    heroInfo->setwSkinID(skinId);
                }
                saveData::arrayUnpackSkin.clear();
            }
        }
        //
    };
    
    uint32_t saveData::heroId = 0;
    uint16_t saveData::skinId = 0;
    bool saveData::enable = false;
    std::vector<std::pair<COMDT_HERO_COMMON_INFO*, uint16_t>> saveData::arrayUnpackSkin;
    //
}

void hook_unpack(CSProtocol::COMDT_HERO_COMMON_INFO* instance) {
    if (!CSProtocol::saveData::enable) {return;}
    if (
    instance->getdwHeroID() == CSProtocol::saveData::heroId
    && CSProtocol::saveData::heroId != 0
    && CSProtocol::saveData::skinId != 0
    ) {
        CSProtocol::saveData::arrayUnpackSkin.emplace_back(instance, instance->getwSkinID());
        instance->setwSkinID(CSProtocol::saveData::skinId);
    }
    
}

TdrErrorType (*_unpack)(CSProtocol::COMDT_HERO_COMMON_INFO* instance, void *tdr, int32_t cutVer);
TdrErrorType unpack(CSProtocol::COMDT_HERO_COMMON_INFO* instance, void *tdr, int32_t cutVer) {

    TdrErrorType result = _unpack(instance, tdr, cutVer);
        if (unlockskin) {
    hook_unpack(instance);
    }
    return result;
    //
}


bool (*_IsCanUseSkin)(void *instance, uint32_t heroId, uint32_t skinId);
bool IsCanUseSkin(void *instance, uint32_t heroId, uint32_t skinId) {

    if (unlockskin) {
        if (heroId != 0) {
        CSProtocol::saveData::setData(heroId, skinId);
    }
    return 1;
    }
    return _IsCanUseSkin(instance, heroId, skinId);

}

bool (*_IsHaveHeroSkin)(uint_t heroId, uint_t skinId, bool isIncludeTimeLimited);
bool IsHaveHeroSkin(uint_t heroId, uint_t skinId, bool isIncludeTimeLimited = false) {
if (unlockskin) {
    return 1;
    }
    return _IsHaveHeroSkin(heroId, skinId, isIncludeTimeLimited);
}

uint32_t (*_WearSkinId)(void* instance, uint32_t heroId);
uint32_t WearSkinId(void* instance, uint32_t heroId) {

if (unlockskin) {
    CSProtocol::saveData::setEnable(true);
    return CSProtocol::saveData::skinId;
    
    }
    
    return _WearSkinId(instance, heroId);
    
}



void *(*_RefreshHeroPanel)(void* ins, bool a, bool b, bool c);
void (*_Setskin)(void *ins,int heroid,int skinid);
void Setskin(void *ins,int heroid,int skinid) {
    
    if (unlockskin && ins != NULL && skinid != 0) {
     _RefreshHeroPanel(ins,true,true,true);   
    }
    _Setskin(ins,heroid,skinid);
}





#include <cmath>

#define OBFUSCATE_METHOD(image, namespaze, clazz, name, args) \
IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE(image), OBFUSCATE(namespaze), OBFUSCATE(clazz), OBFUSCATE(name), args)

#define OBFUSCATE_FIELD(image, namespaze, clazz, name) \
IL2Cpp::Il2CppGetFieldOffset(OBFUSCATE(image), OBFUSCATE(namespaze), OBFUSCATE(clazz), OBFUSCATE(name))

class Camera {
    public:
        static Camera *get_main() {
        Camera *(*get_main_) () = (Camera *(*)())OBFUSCATE_METHOD("UnityEngine.CoreModule.dll", "UnityEngine", "Camera", "get_main", 0);
        return get_main_();
    }
    
    Vector3 WorldToScreenPoint(Vector3 position) {
        Vector3 (*WorldToScreenPoint_)(Camera *camera, Vector3 position) = (Vector3 (*)(Camera *, Vector3))OBFUSCATE_METHOD("UnityEngine.CoreModule.dll", "UnityEngine", "Camera", "WorldToScreenPoint", 1);
        return WorldToScreenPoint_(this, position);
    }
};

class ValueLinkerComponent {
    public:
        int get_actorHp() {
            int (*get_actorHp_)(ValueLinkerComponent * objLinkerWrapper) = (int (*)(ValueLinkerComponent *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ValueLinkerComponent", "get_actorHp", 0);
            return get_actorHp_(this);
        }

        int get_actorHpTotal() {
            int (*get_actorHpTotal_)(ValueLinkerComponent * objLinkerWrapper) =
                (int (*)(ValueLinkerComponent *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ValueLinkerComponent", "get_actorHpTotal", 0);
            return get_actorHpTotal_(this);
        }
};

class ActorConfig {
    public:
        int ConfigID() {
            return *(int *) ((uintptr_t) this + OBFUSCATE_FIELD("Project_d.dll", "Assets.Scripts.GameLogic", "ActorConfig", "ConfigID"));
        }
};

class ActorLinker {
    public:
        ValueLinkerComponent *ValueComponent() {
            return *(ValueLinkerComponent **)((uintptr_t)this + OBFUSCATE_FIELD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "ValueComponent"));
        }

        ActorConfig *ObjLinker() {
            return *(ActorConfig **) ((uintptr_t) this + OBFUSCATE_FIELD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "ObjLinker"));
        }

        Vector3 get_position() {
            Vector3 (*get_position_)(ActorLinker * linker) = (Vector3(*)(ActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "get_position", 0);
            return get_position_(this);
        }
        
        Quaternion get_rotation() {
            Quaternion (*get_rotation_)(ActorLinker *linker) = (Quaternion (*)(ActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "get_rotation", 0);
            return get_rotation_(this);
        }
        
        bool IsHostCamp() {
            bool (*IsHostCamp_)(ActorLinker *linker) = (bool (*)(ActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "IsHostCamp", 0);
            return IsHostCamp_(this);
        }
        
        bool IsHostPlayer() {
            bool (*IsHostPlayer_)(ActorLinker *linker) = (bool (*)(ActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "IsHostPlayer", 0);
            return IsHostPlayer_(this);
        }
        
        bool isMoving() {
            return *(bool *) ((uintptr_t) this + OBFUSCATE_FIELD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "isMoving"));
        }

        Vector3 get_logicMoveForward() {
            Vector3 (*get_logicMoveForward_)(ActorLinker *linker) = (Vector3 (*)(ActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "get_logicMoveForward", 0);
            return get_logicMoveForward_(this);
        }
        
        bool get_bVisible() {
            bool (*get_bVisible_)(ActorLinker *linker) = (bool (*)(ActorLinker *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorLinker", "get_bVisible", 0);
            return get_bVisible_(this);
        }
};

class ActorManager {
    public:
        List<ActorLinker *> *GetAllHeros() {
            List<ActorLinker *> *(*_GetAllHeros)(ActorManager *actorManager) = (List<ActorLinker *> *(*)(ActorManager *))OBFUSCATE_METHOD("Project_d.dll", "Kyrios.Actor", "ActorManager", "GetAllHeros", 0);
            return _GetAllHeros(this);
        }
};

class KyriosFramework {
    public:
        static ActorManager *get_actorManager() {
            auto get_actorManager_ = (ActorManager *(*)())OBFUSCATE_METHOD("Project_d.dll", "Kyrios", "KyriosFramework", "get_actorManager", 0);
            return get_actorManager_();
        }
};

struct EntityInfo {
    Vector3 myPos;
    Vector3 enemyPos;
    Vector3 moveForward;
    int ConfigID;
    bool isMoving;
};

EntityInfo EnemyTarget;

Vector3 RotateVectorByQuaternion(Quaternion q) {
    Vector3 v(0.0f, 0.0f, 1.0f);
    float w = q.W, x = q.X, y = q.Y, z = q.Z;

    Vector3 u(x, y, z);
    Vector3 cross1 = Vector3::Cross(u, v);
    Vector3 cross2 = Vector3::Cross(u, cross1);
    Vector3 result = v + 2.0f * cross1 * w + 2.0f * cross2;

    return result;
}

float SquaredDistance(Vector3 v, Vector3 o) {
    return (v.X - o.X) * (v.X - o.X) + (v.Y - o.Y) * (v.Y - o.Y) + (v.Z - o.Z) * (v.Z - o.Z);
}

Vector3 calculateSkillDirection(Vector3 myPosi, Vector3 enemyPosi, bool isMoving, Vector3 moveForward) {
    if (isMoving) {enemyPosi += moveForward;}
    Vector3 direction = enemyPosi - myPosi;
    direction.Normalize();
    return direction;
}

bool AimElsu;
bool isCharging;
int mode = 0, aimType = 1, drawType = 2, SkillSlott;



float getRange(int configID) {
    switch(configID) {
        case 196: return 25.f;
        case 108: return 13.f;
        case 157: return 13.f;
        case 175: return 13.f;
        case 545: return 13.f;
        default: return 25.f;
    }
}



Vector3 (*_GetUseSkillDirection)(void *instance, bool isTouchUse);
Vector3 GetUseSkillDirection(void *instance, bool isTouchUse) {
    if (instance != NULL && AimElsu) {
        if (EnemyTarget.ConfigID == 196 || EnemyTarget.ConfigID == 108 || EnemyTarget.ConfigID == 157 || EnemyTarget.ConfigID == 175 || EnemyTarget.ConfigID == 545) {
            if (EnemyTarget.myPos != Vector3::Zero() && EnemyTarget.enemyPos != Vector3::Zero() && SkillSlott == 2) {
                return calculateSkillDirection(EnemyTarget.myPos, EnemyTarget.enemyPos, EnemyTarget.isMoving, EnemyTarget.moveForward);
            }
        }
    }
    return _GetUseSkillDirection(instance, isTouchUse);
}

uintptr_t m_isCharging, m_currentSkillSlottType;
void (*_UpdateLogic)(void *instance, int delta);
void UpdateLogic(void *instance, int delta) {
     if (instance != NULL) {
        isCharging = *(bool *)((uintptr_t)instance + OBFUSCATE_FIELD("Project_d.dll", "Assets.Scripts.GameSystem", "CSkillButtonManager", "m_isCharging"));
            SkillSlott = *(int *)((uintptr_t)instance + OBFUSCATE_FIELD("Project_d.dll", "Assets.Scripts.GameSystem", "CSkillButtonManager", "m_currentSkillSlotType"));
    }
    if (AimElsu) {
        Quaternion rotation;
        float minDistance = std::numeric_limits<float>::infinity();
        float minDirection = std::numeric_limits<float>::infinity();
        float minHealth = std::numeric_limits<float>::infinity();
        float minHealth2 = std::numeric_limits<float>::infinity();
        float minHealthPercent = std::numeric_limits<float>::infinity();
        ActorLinker *Entity = nullptr;
        
        ActorManager *get_actorManager = KyriosFramework::get_actorManager();
        if (get_actorManager == nullptr) return;

        List<ActorLinker *> *GetAllHeros = get_actorManager->GetAllHeros();
        if (GetAllHeros == nullptr) return;

        ActorLinker **actorLinkers = (ActorLinker **) GetAllHeros->getItems();

        for (int i = 0; i < GetAllHeros->getSize(); i++) {
            ActorLinker *actorLinker = actorLinkers[(i *2) + 1];
            if (actorLinker == nullptr) continue;
        
            if (actorLinker->IsHostPlayer()) {
                rotation = actorLinker->get_rotation();
                EnemyTarget.myPos = actorLinker->get_position();
                EnemyTarget.ConfigID = actorLinker->ObjLinker()->ConfigID();
            }
        
            if (actorLinker->IsHostCamp() || actorLinker->ValueComponent()->get_actorHp() < 1) continue;
        
            Vector3 EnemyPos = actorLinker->get_position();
            float Health = actorLinker->ValueComponent()->get_actorHp();
            float MaxHealth = actorLinker->ValueComponent()->get_actorHpTotal();
            int HealthPercent = (int)std::round((float)Health / MaxHealth * 100);
            float Distance = Vector3::Distance(EnemyTarget.myPos, EnemyPos);
            float Direction = SquaredDistance(RotateVectorByQuaternion(rotation), calculateSkillDirection(EnemyTarget.myPos, EnemyPos, actorLinker->isMoving(), actorLinker->get_logicMoveForward()));
            
            float range = getRange(EnemyTarget.ConfigID);
            if (Distance < range) {
                if (aimType == 0) {
                    if (HealthPercent < minHealthPercent) {
                        Entity = actorLinker;
                        minHealthPercent = HealthPercent;
                    }
                
                    if (HealthPercent == minHealthPercent && Health < minHealth2) {
                        Entity = actorLinker;
                        minHealth2 = Health;
                        minHealthPercent = HealthPercent;
                    }
                }
            
                if (aimType == 1 && Health < minHealth) {
                    Entity = actorLinker;
                    minHealth = Health;
                }
                
                if (aimType == 2 && Distance < minDistance) {
                    Entity = actorLinker;
                    minDistance = Distance;
                }
            
                if (aimType == 3 && Direction < minDirection && isCharging) {
                    Entity = actorLinker;
                    minDirection = Direction;
                }
            }
        }

        if (Entity == nullptr) {
            EnemyTarget.enemyPos = Vector3::Zero();
            EnemyTarget.moveForward = Vector3::Zero();
            EnemyTarget.ConfigID = 0;
            EnemyTarget.isMoving = false;
        }

        if (Entity != NULL) {
            float nDistance = Vector3::Distance(EnemyTarget.myPos, Entity->get_position());
            if (nDistance > getRange(EnemyTarget.ConfigID) || Entity->ValueComponent()->get_actorHp() < 1) {
                EnemyTarget.enemyPos = Vector3::Zero();
                EnemyTarget.moveForward = Vector3::Zero();
                minDistance = std::numeric_limits<float>::infinity();
                minDirection = std::numeric_limits<float>::infinity();
                minHealth = std::numeric_limits<float>::infinity();
                minHealth2 = std::numeric_limits<float>::infinity();
                minHealthPercent = std::numeric_limits<float>::infinity();
                Entity = nullptr;
            } else {
                EnemyTarget.enemyPos = Entity->get_position();
                EnemyTarget.moveForward = Entity->get_logicMoveForward();
                EnemyTarget.isMoving = Entity->isMoving();
            }
        }
        
        if (Entity != NULL && aimType == 3 && !isCharging) {
            EnemyTarget.enemyPos = Vector3::Zero();
            EnemyTarget.moveForward = Vector3::Zero();
            minDirection = std::numeric_limits<float>::infinity();
            Entity = nullptr;
        }
        
        if ((Entity != NULL || EnemyTarget.enemyPos != Vector3::Zero()) && get_actorManager == nullptr) {
            EnemyTarget.enemyPos = Vector3::Zero();
            EnemyTarget.moveForward = Vector3::Zero();
            minDistance = std::numeric_limits<float>::infinity();
            minDirection = std::numeric_limits<float>::infinity();
            minHealth = std::numeric_limits<float>::infinity();
            minHealth2 = std::numeric_limits<float>::infinity();
            minHealthPercent = std::numeric_limits<float>::infinity();
            Entity = nullptr;
        }
        
        if (drawType != 0 && (EnemyTarget.ConfigID == 196 || EnemyTarget.ConfigID == 108 || EnemyTarget.ConfigID == 157 || EnemyTarget.ConfigID == 175 || EnemyTarget.ConfigID == 545)) {
if (EnemyTarget.myPos != Vector3::Zero() && EnemyTarget.enemyPos != Vector3::Zero()) {
                Vector3 EnemySC = Camera::get_main()->WorldToScreenPoint(EnemyTarget.enemyPos);

              
                if (EnemySC.Z > 0) {
               
                           }
            }
        }
}


    
    
    return _UpdateLogic(instance, delta);
}




// Khai báo các biến và con trỏ hàm cần thiết
void *Enemyc = nullptr;
void *Lactor = nullptr;
void *Lactor2 = nullptr;
void *Req = nullptr;

bool AutoBoTro;
bool AutoPhuTro;
int PhanTramHP;

// Các hàm từ game cần thiết
bool (*ActorLinker_IsHostPlayer)(void *instance);
int (*ActorLinker_ActorTypeDef)(void *instance);
int (*ActorLinker_COM_PLAYERCAMP)(void *instance);
int (*LActorRoot_COM_PLAYERCAMP)(void *instance);
void* (*LActorRoot_LHeroWrapper)(void *instance);
int (*ValuePropertyComponent_get_actorHp)(void *instance);
int (*ValuePropertyComponent_get_actorHpTotal)(void *instance);
bool *(*Reqskill)(void *ins);
Vector3 (*ActorLinker_getPosition)(void *instance);
Vector3 (*Camera_WorldToScreenPoint)(void *instance, Vector3 position);
void* (*Camera_get_main)(void *instance);
VInt3 (*LActorRoot_get_location)(void *instance);
VInt3 (*LActorRoot_get_forward)(void *instance);

// Hàm hỗ trợ chuyển đổi VInt3 sang Vector3
int dem(int num) {
    int div = 1, num1 = num;
    while (num1 != 0) {
        num1 = num1/10;
        div = div*10;
    }
    return div;
}

Vector3 VInt2Vector(VInt3 location, VInt3 forward) {
    return Vector3(
        (float)(location.X*dem(forward.X)+forward.X)/(1000*dem(forward.X)),
        (float)(location.Y*dem(forward.Y)+forward.Y)/(1000*dem(forward.Y)),
        (float)(location.Z*dem(forward.Z)+forward.Z)/(1000*dem(forward.Z))
    );
}

// Hook cho SkillSlot
void (*_Skslot)(void *ins, int del);
void Skslot(void *ins, int del) {
    if (ins != NULL) {
        int slot = *(int *)((uintptr_t)ins + OBFUSCATE_FIELD("Project_d.dll", "Assets.Scripts.GameLogic", "SkillSlot", "SlotType"));
        
        if (slot == 5) {
            Req = ins;    
        }
        
        if (Lactor != NULL) {
            auto Valuec2 = *(uintptr_t *)((uintptr_t)Lactor + OBFUSCATE_FIELD("Project_d.dll","Kyrios.Actor","ActorLinker","ValueComponent"));
            if (Valuec2 != NULL) {
                int Hp = *(int *)((uintptr_t)Valuec2 + OBFUSCATE_FIELD("Project_d.dll","Kyrios.Actor","ValueLinkerComponent","<actorHp>k__BackingField"));
                int Hpt = *(int *)((uintptr_t)Valuec2 + OBFUSCATE_FIELD("Project_d.dll","Kyrios.Actor","ValueLinkerComponent","<actorHpTotal>k__BackingField"));
                float Per = ((float)Hp / (float)Hpt) * 100;
                
                if (AutoBoTro && Per <= PhanTramHP && slot == 9 && PhanTramHP > 1) {
                    Reqskill(ins);
                }
            }
        }
    }
    return _Skslot(ins, del);
}

// Hook cho Wupdate2
void (*_Wupdate2)(void *lol2);
void Wupdate2(void *lol2) {
    if (lol2 != NULL && Lactor != NULL) {
        if (LActorRoot_COM_PLAYERCAMP(lol2) == ActorLinker_COM_PLAYERCAMP(Lactor) && 
            LActorRoot_LHeroWrapper(lol2) != NULL) {
            Enemyc = lol2;
        }
    }
    return _Wupdate2(lol2);
}

// Hook cho Update
void (*old_Update)(void *instance);
void AUpdate(void *instance)
{
    if (instance != NULL)
    {
       Lactor2 = instance; 
       if (ActorLinker_IsHostPlayer(instance)) {
            Lactor = instance;
       }
    }
    return old_Update(instance);
}

// Hook cho Hud3d
void (*_Hud3d)(void *ins, int del);
void Hud3d(void *ins, int del) {
    if (ins != NULL && AutoPhuTro && Req != NULL && Lactor != NULL && Lactor2 != NULL && Enemyc != NULL) {
        auto Valuec2 = *(uintptr_t *)((uintptr_t)Lactor + OBFUSCATE_FIELD("Project_d.dll","Kyrios.Actor","ActorLinker","ValueComponent"));
        auto Valuec3 = *(uintptr_t *)((uintptr_t)Lactor2 + OBFUSCATE_FIELD("Project_d.dll","Kyrios.Actor","ActorLinker","HudControl"));
        
        if (Valuec2 != NULL && Valuec3 != NULL && Camera_get_main(NULL) != NULL) {
            Vector3 MyPlayerPos = ActorLinker_getPosition(Lactor);
            Vector3 EnemyPos2 = VInt2Vector(LActorRoot_get_location(Enemyc), LActorRoot_get_forward(Enemyc));
            Vector3 MyPlayerScreenPos2 = Camera_WorldToScreenPoint(Camera_get_main(NULL), MyPlayerPos);
            Vector3 EnemyScreenPos2 = Camera_WorldToScreenPoint(Camera_get_main(NULL), EnemyPos2);

            int distance2 = (int)Vector3::Distance(MyPlayerScreenPos2, EnemyScreenPos2) / 30;

            void *ValuePropertyComponent = *(void**)((uint64_t)Enemyc + OBFUSCATE_FIELD("Project.Plugins_d.dll", "NucleusDrive.Logic", "LActorRoot", "ValueComponent"));

            if (ValuePropertyComponent != NULL) {
                int Hp2 = ValuePropertyComponent_get_actorHp(ValuePropertyComponent);
                int Hpm2 = ValuePropertyComponent_get_actorHpTotal(ValuePropertyComponent);

                if (Hp2 <= ((Hpm2 - Hp2)*15/100) && Hp2 > 1 && distance2 < 6.3 && distance2 > 0 && AutoPhuTro) {
                    Reqskill(Req);
                }
            }
        }
    }
    return _Hud3d(ins, del);
}



