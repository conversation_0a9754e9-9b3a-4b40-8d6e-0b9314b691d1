#if defined(__aarch64__) 

  


OffsetsOnline();
    
    std::istringstream iss(offset); 
    std::string line;
    while (std::getline(iss, line)) {
        size_t pos1 = line.find(":");
        size_t pos2 = line.find(":", pos1 + 1);
        size_t pos3 = line.find(":", pos2 + 1);
        size_t pos4 = line.find(":", pos3 + 1);
        size_t pos5 = line.find(":", pos4 + 1);
        size_t pos6 = line.find(":", pos5 + 1);
        size_t pos7 = line.find(":", pos6 + 1);

        std::string str1 = line.substr(0, pos1);
        std::string str2 = line.substr(pos1 + 1, pos2 - pos1 - 1);
        std::string str3 = line.substr(pos2 + 1, pos3 - pos2 - 1);
        std::string str4 = line.substr(pos3 + 1, pos4 - pos3 - 1);
        std::string str5 = line.substr(pos4 + 1, pos5 - pos4 - 1);
        std::string str6 = line.substr(pos5 + 1, pos6 - pos5 - 1);
        std::string str7 = line.substr(pos6 + 1, pos7 - pos6 - 1);
        std::string str8 = line.substr(pos7 + 1);
  
        if (str1 == "PatchIl2cpp") {
            MemoryPatch::createWithHex((uintptr_t)IL2Cpp::Il2CppGetMethodOffset(str2.c_str(), str3.c_str(), str4.c_str(), str5.c_str(), std::stoi(str6)), str7.c_str()).Modify();
        } else if (str1 == "PatchAnogs") {
            MemoryPatch::createWithHex("libanogs.so" , string2Offset(str2.c_str()), str3.c_str()).Modify();
        }
    }
    
 
 //MemoryPatch::createWithHex("libanogs.so", string2Offset(OBFUSCATE("0x1340DC")),  OBFUSCATE("000080D2C0035FD6")).Modify(); 
// MemoryPatch::createWithHex((uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LSynchrReport") , OBFUSCATE("SampleFrameSyncData"), 0),"C0035FD6").Modify();
      



 MemoryPatch::createWithHex((uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CGCloudUpdateSystem"), OBFUSCATE("get_IsAutoLogin"), 0),"000080D2C0035FD6").Modify();
    
                 /////////FPS CAO///////////
Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.Framework"), OBFUSCATE("GameSettings") , OBFUSCATE("get_SupportedBoth60FPS_CameraHeight"), 0), (void *) get_Supported60FPSMode, (void **) &old_get_Supported60FPSMode);
Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.Framework"), OBFUSCATE("GameSettings") , OBFUSCATE("get_Supported90FPSMode"), 0), (void *) get_Supported60FPSMode, (void **) &old_get_Supported60FPSMode);
Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.Framework"), OBFUSCATE("GameSettings") , OBFUSCATE("get_Supported120FPSMode"), 0), (void *) get_Supported60FPSMode, (void **) &old_get_Supported60FPSMode);

  ///---Hack map---///
    Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LVActorLinker") , OBFUSCATE("SetVisible"), 3), (void *) LActorRoot_Visible, (void **) &_LActorRoot_Visible);
   ///---CAMERA TUỲ CHỈNH ---///

    OnCameraHeightChanged = (void(*)(void *))IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE(""), OBFUSCATE("CameraSystem") , OBFUSCATE("OnCameraHeightChanged"), 0);
    
    Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE(""), OBFUSCATE("CameraSystem") , OBFUSCATE("Update"), 0), (void *) CameraSystemUpdate, (void **) &old_CameraSystemUpdate);

    Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE(""), OBFUSCATE("CameraSystem") , OBFUSCATE("GetCameraHeightRateValue"), 1), (void *) GetCameraHeightRateValue, (void **) &old_GetCameraHeightRateValue);

   ////---HIỆN UNTI ĐỊCH ---///
   
    
    Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE(""), OBFUSCATE("MiniMapHeroInfo") , OBFUSCATE("ShowSkillStateInfo"), 1), (void *) ShowSkillStateInfo, (void **) &old_ShowSkillStateInfo);
    
    Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE(""), OBFUSCATE("MiniMapHeroInfo") , OBFUSCATE("ShowHeroHpInfo"), 1), (void *) ShowHeroHpInfo, (void **) &old_ShowHeroHpInfo);      
    
    Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("HeroInfoPanel") , OBFUSCATE("ShowHeroInfo"), 2), (void *) ShowHeroInfo, (void **) &old_ShowHeroInfo);

    ///---LỊCH SỬ ĐẤU ---...
    
    Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CPlayerProfile") , OBFUSCATE("get_IsHostProfile"), 0), (void *) get_IsHostProfile, (void **) &old_get_IsHostProfile);
    
               /////////FPS CAO///////////
Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.Framework"), OBFUSCATE("GameSettings") , OBFUSCATE("get_SupportedBoth60FPS_CameraHeight"), 0), (void *) get_Supported60FPSMode, (void **) &old_get_Supported60FPSMode);


/*

///MOD SKIN///

Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("AovTdr.dll"), OBFUSCATE("CSProtocol"), OBFUSCATE("COMDT_HERO_COMMON_INFO") , OBFUSCATE("unpack"), 2), (void *) unpack , (void **) &_unpack);

 Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CRoleInfo") , OBFUSCATE("GetHeroWearSkinId"), 1), (void *) WearSkinId , (void **) &_WearSkinId);

 Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CRoleInfo") , OBFUSCATE("IsCanUseSkin"), 2), (void *) IsCanUseSkin , (void **) &_IsCanUseSkin);

Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CRoleInfo") , OBFUSCATE("IsHaveHeroSkin"), 3), (void *) IsHaveHeroSkin , (void **) &_IsHaveHeroSkin);

Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("HeroSelectNormalWindow") , OBFUSCATE("OnClickSelectHeroSkin"), 2), (void *) Setskin , (void **) &_Setskin);

 _RefreshHeroPanel = (void *(*)(void *,bool,bool,bool))IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("HeroSelectNormalWindow") , OBFUSCATE("RefreshHeroPanel"), 3);
*/
 ///AIM SKILL///

Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameLogic"), OBFUSCATE("SkillControlIndicator") , OBFUSCATE("GetUseSkillDirection"), 1), (void *) GetUseSkillDirection, (void **) &_GetUseSkillDirection);

 Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameSystem"), OBFUSCATE("CSkillButtonManager") , OBFUSCATE("UpdateLogic"), 1), (void *) UpdateLogic, (void **) &_UpdateLogic);

 
 
 //AUTO BỘC PHÁ + BĂNG SƯƠNG//
 
  
Tools::Hook((void *) (uint64_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("Update"), 0), (void *) AUpdate, (void **) &old_Update);

 ActorLinker_IsHostPlayer = (bool (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("IsHostPlayer"), 0);
  
   
LActorRoot_COM_PLAYERCAMP = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot") , OBFUSCATE("GiveMyEnemyCamp"), 0);


ActorLinker_COM_PLAYERCAMP = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("get_objCamp"), 0);

   Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameLogic"), OBFUSCATE("SkillSlot") , OBFUSCATE("LateUpdate"), 1), (void *) Skslot, (void **) &_Skslot);
Reqskill = (bool *(*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameLogic"), OBFUSCATE("SkillSlot") , OBFUSCATE("RequestUseSkill"), 0);

Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Assets.Scripts.GameLogic"), OBFUSCATE("HudComponent3D") , OBFUSCATE("UpdateLogic"), 1), (void *) Hud3d, (void **) &_Hud3d);


LActorRoot_get_forward = (VInt3 (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot") , OBFUSCATE("get_forward"), 0);
LActorRoot_get_location = (VInt3 (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot") , OBFUSCATE("get_location"), 0);

Camera_WorldToScreenPoint = (Vector3 (*)(void *, Vector3))IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("UnityEngine.CoreModule.dll"), OBFUSCATE("UnityEngine"), OBFUSCATE("Camera") , OBFUSCATE("WorldToScreenPoint"), 1);
Camera_get_main = (void* (*)(void *))IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("UnityEngine.CoreModule.dll"), OBFUSCATE("UnityEngine"), OBFUSCATE("Camera") , OBFUSCATE("get_main"), 0);
ActorLinker_getPosition = (Vector3 (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("get_position"), 0);
ActorLinker_ActorTypeDef = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project_d.dll"), OBFUSCATE("Kyrios.Actor"), OBFUSCATE("ActorLinker") , OBFUSCATE("get_objType"), 0);

ValuePropertyComponent_get_actorHp = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("ValuePropertyComponent") , OBFUSCATE("get_actorHp"), 0);
ValuePropertyComponent_get_actorHpTotal = (int (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("ValuePropertyComponent") , OBFUSCATE("get_actorHpTotal"), 0);
LActorRoot_LHeroWrapper = (void* (*)(void *)) IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot") , OBFUSCATE("AsHero"), 0);

Tools::Hook((void *) (uintptr_t)IL2Cpp::Il2CppGetMethodOffset(OBFUSCATE("Project.Plugins_d.dll"), OBFUSCATE("NucleusDrive.Logic"), OBFUSCATE("LActorRoot") , OBFUSCATE("get_gameObject"), 0), (void *) Wupdate2, (void **) &_Wupdate2);
 



    #else
    
    
    
    
    
    
    
    #endif
