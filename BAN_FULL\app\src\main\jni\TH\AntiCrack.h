std::string g_Auth, g_Token;
std::string g_Key, g_DeviceID;
volatile int loginAttempts = 0;
const int MAX_LOGIN_ATTEMPTS = 5;

// Thay thế khai báo biến bValid với cơ chế bảo mật mới
volatile uint32_t secValue1 = 0xA5A5A5A5; // Giá trị mặc định
volatile uint32_t secValue2 = 0xA5A5A5A5; // Ban đầu giống nhau (không hợp lệ)

// Hàm kiểm tra xác thực thay thế cho biến bValid
inline bool isAuthenticated() {
    // Khi xác thực thành công, secValue2 sẽ được đặt thành giá trị khác
    // Phép XOR sẽ tạo ra một giá trị đã biết (0x12345678)
    return (secValue1 ^ secValue2) == 0x12345678 && 
           !g_Token.empty() && !g_Auth.empty() && 
           g_Token == g_Auth;
}


bool check = false;
bool isCheckSize = false;


// Tính MD5 của tệp tin
std::string calculateMD5(const std::string &filePath) {
    unsigned char digest[MD5_DIGEST_LENGTH];
    MD5_CTX ctx;
    MD5_Init(&ctx);
    
    std::ifstream file(filePath, std::ifstream::binary);
    if (!file.is_open()) {
        return "";
    }
    
    char buffer[1024];
    while (file.good()) {
        file.read(buffer, sizeof(buffer));
        MD5_Update(&ctx, buffer, file.gcount());
    }
    file.close();
    
    MD5_Final(digest, &ctx);
    
    std::stringstream ss;
    for (int i = 0; i < MD5_DIGEST_LENGTH; ++i) {
        ss << std::hex << std::setw(2) << std::setfill('0') << (int)digest[i];
    }
    
    return ss.str();
}

// Kiểm tra MD5 của base.apk
std::string checkBaseApkMD5(JNIEnv *env, jobject contextObject) {
    try {
        jclass contextClass = env->GetObjectClass(contextObject);
        if (!contextClass) return "";
        
        jmethodID getPackageManagerMethodID = env->GetMethodID(contextClass, "getPackageManager", "()Landroid/content/pm/PackageManager;");
        if (!getPackageManagerMethodID) {
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        jobject packageManagerObject = env->CallObjectMethod(contextObject, getPackageManagerMethodID);
        if (!packageManagerObject) {
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        jmethodID getPackageNameMethodID = env->GetMethodID(contextClass, "getPackageName", "()Ljava/lang/String;");
        if (!getPackageNameMethodID) {
            env->DeleteLocalRef(packageManagerObject);
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        jstring packageNameString = static_cast<jstring>(env->CallObjectMethod(contextObject, getPackageNameMethodID));
        if (!packageNameString) {
            env->DeleteLocalRef(packageManagerObject);
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        jclass packageManagerClass = env->GetObjectClass(packageManagerObject);
        if (!packageManagerClass) {
            env->DeleteLocalRef(packageNameString);
            env->DeleteLocalRef(packageManagerObject);
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        jmethodID getPackageInfoMethodID = env->GetMethodID(packageManagerClass, "getPackageInfo", "(Ljava/lang/String;I)Landroid/content/pm/PackageInfo;");
        if (!getPackageInfoMethodID) {
            env->DeleteLocalRef(packageManagerClass);
            env->DeleteLocalRef(packageNameString);
            env->DeleteLocalRef(packageManagerObject);
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        jobject packageInfoObject = env->CallObjectMethod(packageManagerObject, getPackageInfoMethodID, packageNameString, 0);
        if (!packageInfoObject) {
            env->DeleteLocalRef(packageManagerClass);
            env->DeleteLocalRef(packageNameString);
            env->DeleteLocalRef(packageManagerObject);
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        jclass packageInfoClass = env->GetObjectClass(packageInfoObject);
        if (!packageInfoClass) {
            env->DeleteLocalRef(packageInfoObject);
            env->DeleteLocalRef(packageManagerClass);
            env->DeleteLocalRef(packageNameString);
            env->DeleteLocalRef(packageManagerObject);
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        jfieldID applicationInfoFieldID = env->GetFieldID(packageInfoClass, "applicationInfo", "Landroid/content/pm/ApplicationInfo;");
        if (!applicationInfoFieldID) {
            env->DeleteLocalRef(packageInfoClass);
            env->DeleteLocalRef(packageInfoObject);
            env->DeleteLocalRef(packageManagerClass);
            env->DeleteLocalRef(packageNameString);
            env->DeleteLocalRef(packageManagerObject);
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        jobject applicationInfoObject = env->GetObjectField(packageInfoObject, applicationInfoFieldID);
        if (!applicationInfoObject) {
            env->DeleteLocalRef(packageInfoClass);
            env->DeleteLocalRef(packageInfoObject);
            env->DeleteLocalRef(packageManagerClass);
            env->DeleteLocalRef(packageNameString);
            env->DeleteLocalRef(packageManagerObject);
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        jclass applicationInfoClass = env->GetObjectClass(applicationInfoObject);
        if (!applicationInfoClass) {
            env->DeleteLocalRef(applicationInfoObject);
            env->DeleteLocalRef(packageInfoClass);
            env->DeleteLocalRef(packageInfoObject);
            env->DeleteLocalRef(packageManagerClass);
            env->DeleteLocalRef(packageNameString);
            env->DeleteLocalRef(packageManagerObject);
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        jfieldID sourceDirFieldID = env->GetFieldID(applicationInfoClass, "sourceDir", "Ljava/lang/String;");
        if (!sourceDirFieldID) {
            env->DeleteLocalRef(applicationInfoClass);
            env->DeleteLocalRef(applicationInfoObject);
            env->DeleteLocalRef(packageInfoClass);
            env->DeleteLocalRef(packageInfoObject);
            env->DeleteLocalRef(packageManagerClass);
            env->DeleteLocalRef(packageNameString);
            env->DeleteLocalRef(packageManagerObject);
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        jstring sourceDirString = static_cast<jstring>(env->GetObjectField(applicationInfoObject, sourceDirFieldID));
        if (!sourceDirString) {
            env->DeleteLocalRef(applicationInfoClass);
            env->DeleteLocalRef(applicationInfoObject);
            env->DeleteLocalRef(packageInfoClass);
            env->DeleteLocalRef(packageInfoObject);
            env->DeleteLocalRef(packageManagerClass);
            env->DeleteLocalRef(packageNameString);
            env->DeleteLocalRef(packageManagerObject);
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        const char *sourceDir = env->GetStringUTFChars(sourceDirString, 0);
        if (!sourceDir) {
            env->DeleteLocalRef(sourceDirString);
            env->DeleteLocalRef(applicationInfoClass);
            env->DeleteLocalRef(applicationInfoObject);
            env->DeleteLocalRef(packageInfoClass);
            env->DeleteLocalRef(packageInfoObject);
            env->DeleteLocalRef(packageManagerClass);
            env->DeleteLocalRef(packageNameString);
            env->DeleteLocalRef(packageManagerObject);
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        std::string baseApkPath = std::string(sourceDir).substr(0, std::string(sourceDir).rfind('/')) + "/base.apk";
        env->ReleaseStringUTFChars(sourceDirString, sourceDir);
        
        // Giải phóng tài nguyên JNI
        env->DeleteLocalRef(sourceDirString);
        env->DeleteLocalRef(applicationInfoClass);
        env->DeleteLocalRef(applicationInfoObject);
        env->DeleteLocalRef(packageInfoClass);
        env->DeleteLocalRef(packageInfoObject);
        env->DeleteLocalRef(packageManagerClass);
        env->DeleteLocalRef(packageNameString);
        env->DeleteLocalRef(packageManagerObject);
        env->DeleteLocalRef(contextClass);
        
        // Tính toán MD5 của base.apk
        return calculateMD5(baseApkPath);
    } catch (...) {
        return "";
    }
}

std::string convertToSignatureFormat(const std::string& md5String) {
    if (md5String.empty()) return "";
    
    std::ostringstream oss;
    for (size_t i = 0; i < md5String.size(); i += 2) {
        if (i > 0) {
            oss << ":";
        }
        if (i + 1 < md5String.size()) {
            oss << std::uppercase << md5String.substr(i, 2);
        } else {
            oss << std::uppercase << md5String.substr(i, 1) << "0";
        }
    }
    return oss.str();
}

// Kiểm tra signature
std::string checkSignature(JNIEnv *env, jobject contextObject) {
    try {
        jclass contextClass = env->GetObjectClass(contextObject);
        if (!contextClass) return "";
        
        jmethodID getPackageManagerMethodID = env->GetMethodID(contextClass, "getPackageManager", "()Landroid/content/pm/PackageManager;");
        jmethodID getPackageNameMethodID = env->GetMethodID(contextClass, "getPackageName", "()Ljava/lang/String;");
        
        if (!getPackageManagerMethodID || !getPackageNameMethodID) {
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        jobject packageManagerObject = env->CallObjectMethod(contextObject, getPackageManagerMethodID);
        jstring packageNameString = static_cast<jstring>(env->CallObjectMethod(contextObject, getPackageNameMethodID));
        
        if (!packageManagerObject || !packageNameString) {
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        jclass packageManagerClass = env->GetObjectClass(packageManagerObject);
        if (!packageManagerClass) {
            env->DeleteLocalRef(packageNameString);
            env->DeleteLocalRef(packageManagerObject);
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        jmethodID getPackageInfoMethodID = env->GetMethodID(packageManagerClass, "getPackageInfo", "(Ljava/lang/String;I)Landroid/content/pm/PackageInfo;");
        if (!getPackageInfoMethodID) {
            env->DeleteLocalRef(packageManagerClass);
            env->DeleteLocalRef(packageNameString);
            env->DeleteLocalRef(packageManagerObject);
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        jobject packageInfoObject = env->CallObjectMethod(packageManagerObject, getPackageInfoMethodID, packageNameString, 64);
        if (!packageInfoObject) {
            env->DeleteLocalRef(packageManagerClass);
            env->DeleteLocalRef(packageNameString);
            env->DeleteLocalRef(packageManagerObject);
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        jclass packageInfoClass = env->GetObjectClass(packageInfoObject);
        if (!packageInfoClass) {
            env->DeleteLocalRef(packageInfoObject);
            env->DeleteLocalRef(packageManagerClass);
            env->DeleteLocalRef(packageNameString);
            env->DeleteLocalRef(packageManagerObject);
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        jfieldID signaturesFieldID = env->GetFieldID(packageInfoClass, "signatures", "[Landroid/content/pm/Signature;");
        if (!signaturesFieldID) {
            env->DeleteLocalRef(packageInfoClass);
            env->DeleteLocalRef(packageInfoObject);
            env->DeleteLocalRef(packageManagerClass);
            env->DeleteLocalRef(packageNameString);
            env->DeleteLocalRef(packageManagerObject);
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        jobjectArray signaturesArray = static_cast<jobjectArray>(env->GetObjectField(packageInfoObject, signaturesFieldID));
        if (!signaturesArray) {
            env->DeleteLocalRef(packageInfoClass);
            env->DeleteLocalRef(packageInfoObject);
            env->DeleteLocalRef(packageManagerClass);
            env->DeleteLocalRef(packageNameString);
            env->DeleteLocalRef(packageManagerObject);
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        // Kiểm tra mảng có phần tử không
        jsize arraySize = env->GetArrayLength(signaturesArray);
        if (arraySize <= 0) {
            env->DeleteLocalRef(signaturesArray);
            env->DeleteLocalRef(packageInfoClass);
            env->DeleteLocalRef(packageInfoObject);
            env->DeleteLocalRef(packageManagerClass);
            env->DeleteLocalRef(packageNameString);
            env->DeleteLocalRef(packageManagerObject);
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        jobject signatureObject = env->GetObjectArrayElement(signaturesArray, 0);
        if (!signatureObject) {
            env->DeleteLocalRef(signaturesArray);
            env->DeleteLocalRef(packageInfoClass);
            env->DeleteLocalRef(packageInfoObject);
            env->DeleteLocalRef(packageManagerClass);
            env->DeleteLocalRef(packageNameString);
            env->DeleteLocalRef(packageManagerObject);
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        jclass signatureClass = env->GetObjectClass(signatureObject);
        if (!signatureClass) {
            env->DeleteLocalRef(signatureObject);
            env->DeleteLocalRef(signaturesArray);
            env->DeleteLocalRef(packageInfoClass);
            env->DeleteLocalRef(packageInfoObject);
            env->DeleteLocalRef(packageManagerClass);
            env->DeleteLocalRef(packageNameString);
            env->DeleteLocalRef(packageManagerObject);
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        jmethodID toByteArrayMethodID = env->GetMethodID(signatureClass, "toByteArray", "()[B");
        if (!toByteArrayMethodID) {
            env->DeleteLocalRef(signatureClass);
            env->DeleteLocalRef(signatureObject);
            env->DeleteLocalRef(signaturesArray);
            env->DeleteLocalRef(packageInfoClass);
            env->DeleteLocalRef(packageInfoObject);
            env->DeleteLocalRef(packageManagerClass);
            env->DeleteLocalRef(packageNameString);
            env->DeleteLocalRef(packageManagerObject);
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        jbyteArray signatureByteArray = static_cast<jbyteArray>(env->CallObjectMethod(signatureObject, toByteArrayMethodID));
        if (!signatureByteArray) {
            env->DeleteLocalRef(signatureClass);
            env->DeleteLocalRef(signatureObject);
            env->DeleteLocalRef(signaturesArray);
            env->DeleteLocalRef(packageInfoClass);
            env->DeleteLocalRef(packageInfoObject);
            env->DeleteLocalRef(packageManagerClass);
            env->DeleteLocalRef(packageNameString);
            env->DeleteLocalRef(packageManagerObject);
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        jbyte* signatureBytes = env->GetByteArrayElements(signatureByteArray, nullptr);
        if (!signatureBytes) {
            env->DeleteLocalRef(signatureByteArray);
            env->DeleteLocalRef(signatureClass);
            env->DeleteLocalRef(signatureObject);
            env->DeleteLocalRef(signaturesArray);
            env->DeleteLocalRef(packageInfoClass);
            env->DeleteLocalRef(packageInfoObject);
            env->DeleteLocalRef(packageManagerClass);
            env->DeleteLocalRef(packageNameString);
            env->DeleteLocalRef(packageManagerObject);
            env->DeleteLocalRef(contextClass);
            return "";
        }
        
        jsize signatureLength = env->GetArrayLength(signatureByteArray);
        
        unsigned char digest[MD5_DIGEST_LENGTH];
        MD5(reinterpret_cast<const unsigned char*>(signatureBytes), signatureLength, digest);
        
        char md5String[MD5_DIGEST_LENGTH * 2 + 1];
        for (int i = 0; i < MD5_DIGEST_LENGTH; i++) {
            sprintf(&md5String[i * 2], "%02x", (unsigned int)digest[i]);
        }
        
        std::string result(md5String, MD5_DIGEST_LENGTH * 2);
        
        env->ReleaseByteArrayElements(signatureByteArray, signatureBytes, JNI_ABORT);
        env->DeleteLocalRef(signatureByteArray);
        env->DeleteLocalRef(signatureClass);
        env->DeleteLocalRef(signatureObject);
        env->DeleteLocalRef(signaturesArray);
        env->DeleteLocalRef(packageInfoClass);
        env->DeleteLocalRef(packageInfoObject);
        env->DeleteLocalRef(packageManagerClass);
        env->DeleteLocalRef(packageManagerObject);
        env->DeleteLocalRef(packageNameString);
        env->DeleteLocalRef(contextClass);
        
        return convertToSignatureFormat(result);
    } catch (...) {
        return "";
    }
}

static std::string GetPackageName() {
    char application_id[256] = {0};
    FILE *fp = fopen("/proc/self/cmdline", "r");
    if (fp) {
        size_t bytesRead = fread(application_id, 1, sizeof(application_id) - 1, fp);
        if (bytesRead > 0) {
            application_id[bytesRead] = '\0';  // Ensure null termination
        }
        fclose(fp);
    }
    return std::string(application_id);
}

bool CheckModVersion() {
    std::string packageName = GetPackageName();
    if (packageName.empty()) {
        return false;
    }
    
    std::string version_file = "/data/data/" + packageName + "/cache/" + std::string(MOD_LIBNAME) + ".version";
    std::ifstream vfile(version_file.c_str());
    if(vfile.good()) {
        std::string version;
        vfile >> version;
        vfile.close();
        return version == MOD_VERSION;
    }
    return false;
}

