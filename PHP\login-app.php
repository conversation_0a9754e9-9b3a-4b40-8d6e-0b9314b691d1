<?php
// Thiết lập header JSON
header('Content-Type: application/json; charset=utf-8');
error_reporting(0);

// Khởi động session nếu chưa
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Kết nối đến cơ sở dữ liệu
include_once("config/config.php");

// Hàm ghi log lỗi an toàn
function safeErrorLog($message) {
    $logDir = 'logs';
    if (!is_dir($logDir)) {
        @mkdir($logDir, 0755, true);
    }
    $logFile = $logDir . '/error_' . date('Y-m-d') . '.log';
    error_log(date('[Y-m-d H:i:s] ') . $message . PHP_EOL, 3, $logFile);
}

// Hàm chuyển đổi memory limit string thành bytes
function return_bytes($val) {
    $val = trim($val);
    $last = strtolower($val[strlen($val)-1]);
    $val = (int)$val;
    switch($last) {
        case 'g':
            $val *= 1024;
        case 'm':
            $val *= 1024;
        case 'k':
            $val *= 1024;
    }
    return $val;
}

// Các hàm tiện ích
function IsSafe($string) {
   return preg_match('/[^a-zA-Z0-9_-]/', $string) == 0;
}

function generateToken($key, $deviceid) {
   return md5("lqm-" . $key . "-" . $deviceid . "-Vm8Lk7Uj2JmsjCPVPVjrLa7zgfx3uz9E");
}

// Kiểm tra phiên bản - đã sửa đổi để kiểm tra chặt chẽ hơn và sử dụng prepared statement
function checkVersion($con, $version, $apk_md5, $signature, $game_code) {
    // Các mã lỗi:
    // 0 = OK (không có lỗi)
    // 1 = Phiên bản bị vô hiệu hóa
    // 2 = MD5 không khớp
    // 3 = Signature không khớp
    // 4 = Phiên bản không tồn tại

    // Kiểm tra xem có các tham số cần thiết không
    if (empty($version) || empty($game_code)) {
        return 4; // Trả về lỗi nếu thiếu thông tin cơ bản
    }

    // Kiểm tra bảng có tồn tại không
    $result = $con->query("SHOW TABLES LIKE 'version_config'");
    if ($result->num_rows == 0) {
        return 4; // Trả về lỗi nếu bảng không tồn tại
    }

    // Kiểm tra bảng mod_app_mapping có tồn tại không
    $result = $con->query("SHOW TABLES LIKE 'mod_app_mapping'");
    if ($result->num_rows == 0) {
        // Tạo bảng mod_app_mapping nếu chưa tồn tại
        $sql = "CREATE TABLE `mod_app_mapping` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `version_config_id` int(11) NOT NULL,
          `app_identifier` varchar(100) NOT NULL,
          PRIMARY KEY (`id`),
          UNIQUE KEY `version_app` (`version_config_id`, `app_identifier`),
          CONSTRAINT `fk_version_config` FOREIGN KEY (`version_config_id`) REFERENCES `version_config` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";

        $con->query($sql);
    }

    // Trước tiên, kiểm tra xem phiên bản có tồn tại trong DB không
    $stmt = $con->prepare("SELECT * FROM version_config WHERE version = ? AND game_code = ?");
    $stmt->bind_param("ss", $version, $game_code);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows == 0) {
        $stmt->close();
        return 4; // Trả về lỗi nếu không tìm thấy phiên bản
    }

    $row = $result->fetch_assoc();
    $stmt->close();

    // Kiểm tra xem phiên bản có đang kích hoạt hay không
    if ($row['is_active'] != 1) {
        return 1; // Trả về 1 nếu phiên bản bị vô hiệu hóa
    }

    // So sánh MD5 và signature nếu được cung cấp
    if (!empty($apk_md5) && $row['apk_md5'] != $apk_md5) {
        return 2; // Trả về 2 nếu MD5 không khớp
    }

    if (!empty($signature) && $row['signature'] != $signature) {
        return 3; // Trả về 3 nếu signature không khớp
    }

    return 0; // Trả về 0 nếu tất cả đều khớp
}

// Khởi tạo response
$resinfo = array();

// Kiểm tra bảo trì
if(isset($_SESSION["baotri"]) && $_SESSION["baotri"] == "yes") {
   $resinfo["bool"] = "false";
   $resinfo["mes"] = "Web Site Đang Bảo Trì 🔧";
   exit(json_encode($resinfo));
}

// Xử lý login với key
if(isset($_POST["key"])) {
    $key = $_POST["key"];
    $deviceid = isset($_POST["deviceid"]) ? $_POST["deviceid"] : "";
    $gamecode = isset($_POST["gamecode"]) ? $_POST["gamecode"] : "";
    $version = isset($_POST["version"]) ? $_POST["version"] : "N/A";
    $apk_md5 = isset($_POST["apk_md5"]) ? $_POST["apk_md5"] : "";
    $signature = isset($_POST["signature"]) ? $_POST["signature"] : "";
    $mod_description = isset($_POST["mod_description"]) ? $_POST["mod_description"] : "N/A";
    $key_id = isset($_POST["key_id"]) ? $_POST["key_id"] : "";

    // Lấy thông tin thiết bị từ request
    $device_info = isset($_POST["device_info"]) ? $_POST["device_info"] : "Android";

    // Phân tích thông tin thiết bị
    $deviceModel = $device_info;
    $androidVersion = "Unknown";
    $buildVersion = "Unknown";

    // Phân tích chuỗi thông tin thiết bị để lấy các thành phần (bỏ phần IMEI)
    if (preg_match('/(.+?) \(Android (.+?), Build: (.+?)\)/', $device_info, $matches)) {
        $deviceModel = $matches[1];
        $androidVersion = $matches[2];
        $buildVersion = $matches[3];
    }

    // Kiểm tra tham số bắt buộc
    if (empty($deviceid) || empty($gamecode)) {
        $resinfo["bool"] = "false";
        $resinfo["mes"] = "Thiếu thông tin thiết bị hoặc mã game!";
        exit(json_encode($resinfo));
    }

    // Kiểm tra phiên bản
    $versionCheck = checkVersion($con, $version, $apk_md5, $signature, $gamecode);
    if ($versionCheck != 0) {
        $resinfo["bool"] = "false";

        switch ($versionCheck) {
            case 1:
                $resinfo["mes"] = "Phiên bản này đã bị vô hiệu hóa. Vui lòng cập nhật lên phiên bản mới!";
                break;
            case 2:
                $resinfo["mes"] = "Kiểm tra tính toàn vẹn của ứng dụng thất bại. APK có thể đã bị chỉnh sửa!";
                break;
            case 3:
                $resinfo["mes"] = "Chữ ký ứng dụng không hợp lệ. Vui lòng cài đặt ứng dụng từ nguồn chính thức!";
                break;
            case 4:
                $resinfo["mes"] = "Phiên bản này không được hỗ trợ. Vui lòng cập nhật lên phiên bản chính thức!";
                break;
            default:
                $resinfo["mes"] = "Không thể xác thực phiên bản. Vui lòng liên hệ admin!";
        }

        exit(json_encode($resinfo));
    }

    // Kiểm tra key
    if(strlen($key) < 10) {
        $resinfo["bool"] = "false";
        $resinfo["mes"] = "Key không hợp lệ vui lòng kiểm tra lại!";
        exit(json_encode($resinfo));
    }

    if(!IsSafe($key)) {
        $resinfo["bool"] = "false";
        $resinfo["mes"] = "Định dạng key không chính xác!";
        exit(json_encode($resinfo));
    }

    // Kiểm tra key trong database - cải thiện bằng prepared statement
    $sql = "SELECT * FROM keytab WHERE tenkey = ?";
    $stmt = $con->prepare($sql);
    $stmt->bind_param("s", $key);
    $stmt->execute();
    $result = $stmt->get_result();

    if($result->num_rows == 0) {
        $stmt->close();
        $resinfo["bool"] = "false";
        $resinfo["mes"] = "Key không tồn tại trong hệ thống!";
        exit(json_encode($resinfo));
    }

    $row = $result->fetch_assoc();
    $stmt->close();

    if($row["game"] != $gamecode) {
        $resinfo["bool"] = "false";
        $resinfo["mes"] = "Key này hiện tại không thể sử dụng với MOD này!";
        exit(json_encode($resinfo));
    }

    $trangthai = $row["trangthai"];
    $code = $row["code"];
    $daban = $row["daban"];
    $ngaykichhoat = $row["ngaykichhoat"];
    $hansudung = $row["hansudung"];

    // Kiểm tra trạng thái key
    if($trangthai == "hoatdong") {
        if(strtotime($hansudung) < strtotime(date("Y-m-d H:i:s"))) {
            $resinfo["bool"] = "false";
            $resinfo["mes"] = "Key đã hết hạn sử dụng!";
            exit(json_encode($resinfo));
        }
    } elseif($trangthai == "dangcho") {
        // Kích hoạt key mới
        $date = date("Y-m-d H:i:s");
        $datetime = new DateTime($date);
        $datetime->modify('+'.$hansudung.' day');
        $hansudung = $datetime->format('Y-m-d H:i:s');

        // Cải thiện bằng prepared statement
        $updateSql = "UPDATE keytab SET trangthai='hoatdong', ngaykichhoat=?, hansudung=? WHERE tenkey=?";
        $updateStmt = $con->prepare($updateSql);
        $updateStmt->bind_param("sss", $date, $hansudung, $key);
        $updateStmt->execute();
        $updateStmt->close();
    } else {
        $resinfo["bool"] = "false";
        $resinfo["mes"] = "Key đã bị khóa!";
        exit(json_encode($resinfo));
    }

    // Xử lý thiết bị
    $_may = $row["may"];
    $may = json_decode($_may, true);

    // Kiểm tra thiết bị
    $deviceFound = false;
    if (is_array($may)) {
        foreach ($may as &$device) {
            if ($device === $deviceid || $device === "Empty") {
                $device = $deviceid; // Gán device ID vào vị trí này
                $deviceFound = true;
                break;
            }
        }
    }

    // Nếu không tìm thấy slot phù hợp
    if (!$deviceFound) {
        $resinfo["bool"] = "false";
        $resinfo["mes"] = "Đăng nhập quá thiết bị cho phép, liên hệ admin!";
        exit(json_encode($resinfo));
    }

    // Cập nhật thiết bị với prepared statement
    $may_json = json_encode($may);
    $updateDeviceSql = "UPDATE keytab SET may=? WHERE tenkey=?";
    $updateDeviceStmt = $con->prepare($updateDeviceSql);
    $updateDeviceStmt->bind_param("ss", $may_json, $key);

    if($updateDeviceStmt->execute()) {
        $updateDeviceStmt->close();

        // Cập nhật trạng thái bán
        if($daban == "no") {
            // Cải thiện cập nhật số liệu bán hàng
            $updateSellSql = "UPDATE admin SET sokeydaban=sokeydaban+1 WHERE code=?";
            $updateSellStmt = $con->prepare($updateSellSql);
            $updateSellStmt->bind_param("s", $code);
            $updateSellStmt->execute();
            $updateSellStmt->close();

            $updateKeySql = "UPDATE keytab SET daban='yes' WHERE tenkey=?";
            $updateKeyStmt = $con->prepare($updateKeySql);
            $updateKeyStmt->bind_param("s", $key);
            $updateKeyStmt->execute();
            $updateKeyStmt->close();
        }

        // Lấy tên người bán
        $adminSql = "SELECT hovaten FROM admin WHERE code = ?";
        $adminStmt = $con->prepare($adminSql);
        $adminStmt->bind_param("s", $code);
        $adminStmt->execute();
        $adminResult = $adminStmt->get_result();
        $adminInfo = $adminResult->fetch_assoc();
        $hovaten = isset($adminInfo['hovaten']) ? $adminInfo['hovaten'] : "H-MOD User";
        $adminStmt->close();

        // Trả về thông tin thành công
        $resinfo["bool"] = "true";
        $resinfo["o"] = array(
            "key" => $key,
            "hsd" => $hansudung
        );
        $resinfo["mes"] = "Đăng nhập thành công!";
        $resinfo["token"] = generateToken($key, $deviceid);
        $resinfo["in4"] = base64_encode(json_encode($resinfo["o"]));
        $resinfo["user"] = $hovaten;

        // Chuẩn bị phản hồi trước khi lưu log
        $response = json_encode($resinfo);

        // Lấy thông tin tiêu đề MOD từ version_config
        $mod_title = "";
        if (!empty($version)) {
            $mod_query = "SELECT title FROM version_config WHERE version = ? AND game_code = ? LIMIT 1";
            $mod_stmt = $con->prepare($mod_query);
            if ($mod_stmt) {
                $mod_stmt->bind_param("ss", $version, $gamecode);
                $mod_stmt->execute();
                $mod_result = $mod_stmt->get_result();
                if ($mod_result && $mod_result->num_rows > 0) {
                    $mod_row = $mod_result->fetch_assoc();
                    $mod_title = $mod_row['title'];
                }
                $mod_stmt->close();
            }
        }

        // Ghi log vào bảng login_history với prepared statement
try {
    // Kiểm tra bảng login_history có tồn tại không
    $checkTable = $con->query("SHOW TABLES LIKE 'login_history'");
    if (!$checkTable || $checkTable->num_rows == 0) {
        // Tạo bảng login_history nếu không tồn tại
        $createTable = "CREATE TABLE IF NOT EXISTS login_history (
            id INT AUTO_INCREMENT PRIMARY KEY,
            key_id VARCHAR(255) NOT NULL,
            key_value VARCHAR(255) NOT NULL,
            device_info TEXT,
            device_model VARCHAR(255),
            android_version VARCHAR(50),
            build_version VARCHAR(50),
            game_code VARCHAR(50),
            mod_version VARCHAR(50),
            mod_title VARCHAR(255),
            user_ip VARCHAR(45),
            login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        if (!$con->query($createTable)) {
            safeErrorLog("Lỗi tạo bảng login_history: " . $con->error);
        }
    }

    // Đặt key_id bằng id của key trong bảng keytab (nếu có) hoặc dùng key_id từ request
    $key_id = isset($row['id']) ? $row['id'] : (isset($_POST["key_id"]) ? $_POST["key_id"] : $key);

    // Sử dụng prepared statement cho INSERT với đầy đủ trường
    $insertStmt = $con->prepare("INSERT INTO login_history
        (key_id, key_value, device_info, device_model, android_version, build_version,
         game_code, mod_version, mod_title, user_ip)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

    $ipAddress = $_SERVER['REMOTE_ADDR'];
    $insertStmt->bind_param("ssssssssss",
        $key_id,           // key_id
        $key,              // key_value
        $device_info,      // device_info (toàn bộ thông tin thiết bị)
        $deviceModel,      // device_model
        $androidVersion,   // android_version
        $buildVersion,     // build_version
        $gamecode,         // game_code
        $version,          // mod_version
        $mod_title,        // mod_title
        $ipAddress         // user_ip
    );

    if (!$insertStmt->execute()) {
        safeErrorLog("Lỗi ghi login_history: " . $insertStmt->error);
    }
    $insertStmt->close();
} catch (Exception $e) {
    safeErrorLog("Exception khi ghi login_history: " . $e->getMessage());
}

        // Gửi response cho người dùng
        header('Content-Length: ' . strlen($response));
        echo $response;

        // Đảm bảo dữ liệu được gửi đi
        if (ob_get_level() > 0) {
            ob_end_flush();
        }
        flush();

        // Bây giờ gửi thông báo Discord trực tiếp mà không chặn response
        if (function_exists('curl_init')) {
            try {
                $loginTime = date("Y-m-d H:i:s");

                // Tạo thư mục logs nếu chưa tồn tại
                $logDir = 'logs';
                if (!is_dir($logDir)) {
                    @mkdir($logDir, 0755, true);
                }

                // Discord Bot configuration
                $botToken = "YOUR_BOT_TOKEN_HERE"; // Thay bằng bot token của bạn
                $channelIds = [
                    "1234567890123456789", // Channel ID 1
                    "9876543210987654321"  // Channel ID 2
                    // Thêm nhiều channel nếu cần
                ];

                // Tạo embed message cho Discord
                $embed = [
                    "title" => "🔐 Thông báo đăng nhập",
                    "color" => 3447003, // Màu xanh dương
                    "timestamp" => date('c'), // ISO 8601 format
                    "fields" => [
                        [
                            "name" => "📌 Key",
                            "value" => "`{$key}`",
                            "inline" => true
                        ],
                        [
                            "name" => "👤 Seller",
                            "value" => $hovaten,
                            "inline" => true
                        ],
                        [
                            "name" => "⏰ Thời gian",
                            "value" => $loginTime,
                            "inline" => true
                        ],
                        [
                            "name" => "📱 Thông tin thiết bị",
                            "value" => "**Model:** {$deviceModel}\n**Android:** {$androidVersion}\n**Build:** {$buildVersion}",
                            "inline" => false
                        ],
                        [
                            "name" => "🔑 Thông tin KEY",
                            "value" => "**Kích hoạt:** {$ngaykichhoat}\n**Hạn sử dụng:** {$hansudung}\n**Game Code:** {$gamecode}",
                            "inline" => false
                        ],
                        [
                            "name" => "🔖 Phiên bản",
                            "value" => ($version ?? 'N/A'),
                            "inline" => true
                        ],
                        [
                            "name" => "📝 Loại MOD",
                            "value" => ($mod_description ?? 'N/A'),
                            "inline" => true
                        ]
                    ],
                    "footer" => [
                        "text" => "H-MOD Login System",
                        "icon_url" => "https://cdn.discordapp.com/attachments/123456789/avatar.png" // Thay bằng icon của bạn
                    ]
                ];

                $payload = [
                    "embeds" => [$embed]
                ];

                // Gửi thông báo đến từng Discord channel thông qua bot
                foreach ($channelIds as $channelId) {
                    $url = "https://discord.com/api/v10/channels/{$channelId}/messages";

                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $url);
                    curl_setopt($ch, CURLOPT_POST, 1);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
                    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 3);
                    curl_setopt($ch, CURLOPT_HTTPHEADER, [
                        'Content-Type: application/json',
                        'Authorization: Bot ' . $botToken
                    ]);
                    @curl_exec($ch);
                    curl_close($ch);
                }
            } catch (Exception $e) {
                safeErrorLog("Lỗi khi gửi thông báo Discord: " . $e->getMessage());
            }
        }

        exit;
    } else {
        $updateDeviceStmt->close();
        safeErrorLog("Lỗi cập nhật thiết bị cho key $key: " . $con->error);
        $resinfo["bool"] = "false";
        $resinfo["mes"] = "Lỗi cập nhật thông tin thiết bị!";
        exit(json_encode($resinfo));
    }
}

// Đoạn code kiểm tra trạng thái mod (thay thế phần đọc từ JSON)
if(isset($_POST["check_status"])) {
    // Lấy game_code và app_identifier từ request
    $game_code = isset($_POST["gamecode"]) ? $_POST["gamecode"] : "";
    $app_identifier = isset($_POST["app_identifier"]) ? $_POST["app_identifier"] : "";

    // Đảm bảo trả về JSON hợp lệ ngay cả khi có lỗi
    try {
        // Kiểm tra bảng mod_app_mapping có tồn tại không
        $tableExists = false;
        $checkTable = $con->query("SHOW TABLES LIKE 'mod_app_mapping'");
        if ($checkTable && $checkTable->num_rows > 0) {
            $tableExists = true;
        }

        // Lấy danh sách tất cả các MOD có is_active = 1 và game_code phù hợp
        $query = "SELECT id, version, lib_name, safety, safety_message, description, icon, title, status, file_path, vpn_check_enabled
                  FROM version_config
                  WHERE is_active = 1";

        if (!empty($game_code)) {
            $query .= " AND game_code = ?";
            $stmt = $con->prepare($query);
            $stmt->bind_param("s", $game_code);
        } else {
            $stmt = $con->prepare($query);
        }

        $stmt->execute();
        $result = $stmt->get_result();

        // Khởi tạo mảng để lưu thông tin MOD
        $mod_info = array();

        // Nếu có kết quả
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $lib_name = $row['lib_name'];
                $version_id = $row['id'];

                // Bỏ qua nếu lib_name trống
                if (empty($lib_name)) continue;

                // Mặc định hiển thị MOD
                $show_mod = true;

                // Nếu có app_identifier và bảng mod_app_mapping tồn tại
                if (!empty($app_identifier) && $tableExists) {
                    // Kiểm tra xem MOD này có bản ghi nào trong bảng mod_app_mapping không
                    $map_query = "SELECT COUNT(*) as count FROM mod_app_mapping WHERE version_config_id = ?";
                    $map_stmt = $con->prepare($map_query);
                    $map_stmt->bind_param("i", $version_id);
                    $map_stmt->execute();
                    $map_result = $map_stmt->get_result();
                    $map_row = $map_result->fetch_assoc();
                    $has_mappings = ($map_row['count'] > 0);
                    $map_stmt->close();

                    // Nếu MOD có bản ghi trong bảng mod_app_mapping
                    if ($has_mappings) {
                        // Kiểm tra xem app_identifier này có được phép hiển thị MOD này không
                        $app_query = "SELECT COUNT(*) as count FROM mod_app_mapping
                                      WHERE version_config_id = ? AND app_identifier = ?";
                        $app_stmt = $con->prepare($app_query);
                        $app_stmt->bind_param("is", $version_id, $app_identifier);
                        $app_stmt->execute();
                        $app_result = $app_stmt->get_result();
                        $app_row = $app_result->fetch_assoc();
                        $show_mod = ($app_row['count'] > 0);
                        $app_stmt->close();
                    }
                }

                // Nếu MOD được hiển thị
                if ($show_mod) {
                    // Thêm thông tin MOD vào mảng kết quả
                    $mod_info[$lib_name] = array(
                        'version' => $row['version'],
                        'safety' => $row['safety'],
                        'safety_message' => $row['safety_message'],
                        'description' => $row['description'],
                        'icon' => $row['icon'],
                        'title' => $row['title'],
                        'status' => $row['status'],
                        'vpn_check_enabled' => (bool)$row['vpn_check_enabled']
                    );

                    // Kiểm tra file có tồn tại không
                    $file_exists = false;
                    $file_path = empty($row['file_path']) ? "files/" . $lib_name : $row['file_path'];
                    if (file_exists($file_path)) {
                        $file_exists = true;
                    } elseif (file_exists(dirname(__FILE__) . "/" . $file_path)) {
                        $file_exists = true;
                    }

                    $mod_info[$lib_name]['status'] = $file_exists ? 'ready' : 'not_ready';
                }
            }
        }

        $stmt->close();

        // Trả về kết quả
        $response = array(
            "status" => "success",
            "data" => $mod_info
        );

        exit(json_encode($response));
    } catch (Exception $e) {
        // Ghi log lỗi
        error_log("Error in check_status: " . $e->getMessage());

        // Trả về lỗi
        exit(json_encode(array(
            "status" => "error",
            "message" => "Đã xảy ra lỗi, vui lòng thử lại sau.",
            "data" => array() // Đảm bảo luôn có trường data
        )));
    }
}

// Đoạn code xử lý download lib
else if(isset($_POST["libname"])) {
    try {
        $libname = isset($_POST["libname"]) ? $_POST["libname"] : "";
        $game_code = isset($_POST["gamecode"]) ? $_POST["gamecode"] : "";
        $app_identifier = isset($_POST["app_identifier"]) ? $_POST["app_identifier"] : "";

        // Kiểm tra tham số bắt buộc
        if (empty($libname) || empty($game_code)) {
            exit(json_encode(array(
                "status" => "error",
                "message" => "Thiếu thông tin cần thiết!"
            )));
        }

        // Kiểm tra tên file an toàn
        if(!preg_match('/^[a-zA-Z0-9_\-\.]+$/', $libname)) {
            exit(json_encode(array(
                "status" => "error",
                "message" => "Tên file không hợp lệ!"
            )));
        }

        // Kiểm tra bảng mod_app_mapping có tồn tại không
        $tableExists = false;
        $checkTable = $con->query("SHOW TABLES LIKE 'mod_app_mapping'");
        if ($checkTable && $checkTable->num_rows > 0) {
            $tableExists = true;
        }

        // Truy vấn SQL để lấy thông tin MOD với điều kiện game_code
        $stmt = $con->prepare("SELECT id, version, file_path FROM version_config WHERE lib_name = ? AND is_active = 1 AND game_code = ?");
        $stmt->bind_param("ss", $libname, $game_code);
        $stmt->execute();
        $result = $stmt->get_result();

        if (!$result || $result->num_rows == 0) {
            $stmt->close();
            exit(json_encode(array(
                "status" => "error",
                "message" => "Không tìm thấy MOD cho game code: " . $game_code
            )));
        }

        $row = $result->fetch_assoc();
        $version_id = $row['id'];
        $show_mod = true;

        // Nếu có app_identifier và bảng mod_app_mapping tồn tại
        if (!empty($app_identifier) && $tableExists) {
            // Kiểm tra xem MOD này có bản ghi nào trong bảng mod_app_mapping không
            $map_query = "SELECT COUNT(*) as count FROM mod_app_mapping WHERE version_config_id = ?";
            $map_stmt = $con->prepare($map_query);
            $map_stmt->bind_param("i", $version_id);
            $map_stmt->execute();
            $map_result = $map_stmt->get_result();
            $map_row = $map_result->fetch_assoc();
            $has_mappings = ($map_row['count'] > 0);
            $map_stmt->close();

            // Nếu MOD có bản ghi trong bảng mod_app_mapping
            if ($has_mappings) {
                // Kiểm tra xem app_identifier này có được phép hiển thị MOD này không
                $app_query = "SELECT COUNT(*) as count FROM mod_app_mapping
                              WHERE version_config_id = ? AND app_identifier = ?";
                $app_stmt = $con->prepare($app_query);
                $app_stmt->bind_param("is", $version_id, $app_identifier);
                $app_stmt->execute();
                $app_result = $app_stmt->get_result();
                $app_row = $app_result->fetch_assoc();
                $show_mod = ($app_row['count'] > 0);
                $app_stmt->close();
            }
        }

        // Nếu MOD không được hiển thị cho app_identifier này
        if (!$show_mod) {
            $stmt->close();
            exit(json_encode(array(
                "status" => "error",
                "message" => "MOD này không khả dụng cho ứng dụng của bạn."
            )));
        }

        // Lấy thông tin file
        $file_path = empty($row['file_path']) ? "files/" . $libname : $row['file_path'];
        $version = $row['version'];
        $stmt->close();

        // Kiểm tra file có tồn tại không
        $file_content = null;
        $actual_file_path = null;

        if (file_exists($file_path)) {
            $actual_file_path = $file_path;
        } elseif (file_exists(dirname(__FILE__) . "/" . $file_path)) {
            $actual_file_path = dirname(__FILE__) . "/" . $file_path;
        }

        if ($actual_file_path !== null) {
            // Kiểm tra kích thước file trước khi load
            $file_size = filesize($actual_file_path);
            $max_file_size = 50 * 1024 * 1024; // 50MB limit

            if ($file_size > $max_file_size) {
                exit(json_encode(array(
                    "status" => "error",
                    "message" => "File quá lớn để tải xuống!"
                )));
            }

            // Kiểm tra memory available
            $memory_needed = $file_size * 2; // Base64 encoding needs ~1.33x + overhead
            $memory_limit = ini_get('memory_limit');
            $memory_available = return_bytes($memory_limit) - memory_get_usage();

            if ($memory_needed > $memory_available) {
                exit(json_encode(array(
                    "status" => "error",
                    "message" => "Không đủ bộ nhớ để xử lý file!"
                )));
            }

            $file_content = file_get_contents($actual_file_path);

            if ($file_content !== false) {
                $response = array(
                    "status" => "success",
                    "version" => $version,
                    "data" => base64_encode($file_content)
                );
                exit(json_encode($response));
            } else {
                exit(json_encode(array(
                    "status" => "error",
                    "message" => "Không thể đọc file dữ liệu!"
                )));
            }
        } else {
            exit(json_encode(array(
                "status" => "error",
                "message" => "File dữ liệu không tồn tại!"
            )));
        }
    } catch (Exception $e) {
        // Ghi log lỗi
        error_log("Error in download lib: " . $e->getMessage());

        // Trả về lỗi
        exit(json_encode(array(
            "status" => "error",
            "message" => "Đã xảy ra lỗi, vui lòng thử lại sau."
        )));
    }
}

// Xử lý các request không hợp lệ
exit(json_encode(array(
    "status" => "error",
    "message" => "Invalid request"
)));